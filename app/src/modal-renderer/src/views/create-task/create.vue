/// <reference path="../../../../common/link-hub/impl/thunder-client-api.d.ts" />
<script setup lang="ts">
// ==================== 第三方库导入 ====================
import path from 'node:path'
import { onMounted, ref, watch, computed, onUnmounted, nextTick, provide } from 'vue'
import { storeToRefs } from 'pinia'
import { useEventListener } from '@vueuse/core'

// ==================== 基础类型和常量 ====================
import * as BaseType from '@root/common/task/base'
import { MAX_MAGNET_FILE_COUNT } from '@root/modal-renderer/src/utils/constants'
import { config } from '@root/common/config/config'

// ==================== UI组件 ====================
//@ts-ignore
import { useAlertDialog } from '@root/common/components/ui/Dialog/useAlertDialog.js'
import Dialog from '@root/common/components/ui/Dialog/Dialog.vue'
import XMPMessage from '@root/common/components/ui/message/index'

// ==================== 业务组件 ====================
import TaskList from '@root/modal-renderer/src/components/new-task/task-list/task-list.vue'
import DownloadButton from '@root/modal-renderer/src/components/new-task/download-button/download-button.vue'
import PlayButton from '@root/modal-renderer/src/components/new-task/play-button/PlayButton.vue'
import TaskLaterButton from '@root/modal-renderer/src/components/new-task/task-later-button/TaskLaterButton.vue'
import TextareaContextMenu from '@root/modal-renderer/src/components/new-task/textarea-context-menu/textarea-context-menu.vue'
import AutoCreateTaskComponent from '@root/modal-renderer/src/views/create-task/auto-create.vue'

// ==================== 核心服务 ====================
import { TaskManager } from '@root/common/task/client/task-manager'
import { ThunderNewTaskHelperNS } from '@root/common/task/client/new-task-helper'
import { Task } from '@root/common/task/client/task'
import { DkHelper } from '@root/common/task/impl/dk-helper'
import { ThunderPanClientSDK } from '@root/common/thunder-pan-manager/client'

import { ThunderHelper } from '@root/common/thunder-helper'

// ==================== 辅助工具 ====================
import { FileOperationHelper } from '@root/common/helper/file-operation-helper'

import { DownloadPathNS } from '@root/common/config/download-path'
import { getTorrentFilePath, createTaskError } from '@root/modal-renderer/src/utils/new-task-util'
import {
  detectFileType,
  isFtpUrl,
  parseMultilineUrls,
  updateMediaLists,
  generateDefaultMagnetTaskName,
  extractFileNameFromUrl as extractFileNameFromUrlUtil,
} from '@root/modal-renderer/src/utils.help'
import {
  LinkSaver,
  LinkSaveScene,
  type LinkSaveOptions,
} from '@root/modal-renderer/src/utils/link-saver'

// ==================== 弹窗管理 ====================
import { PopUpNS } from '@root/common/pop-up'
import * as PopUpTypes from '@root/common/pop-up/types'

// ==================== 状态管理 ====================
import { useCreateTaskStore } from '@root/modal-renderer/src/stores/create-task'
import { useDownloadPathStore } from '@root/modal-renderer/src/stores/download-path'
import { useTaskSettingStore } from '@root/modal-renderer/src/stores/task-setting'
import { useUserStore } from '@root/modal-renderer/src/stores/user'
import { useDownloadCloudPathStore } from '@root/modal-renderer/src/stores/download-cloud-path'


// ==================== 组合式函数 ====================
import { usePositionMixinComponent } from '@root/modal-renderer/src/common/mixins'
import { useAuth } from '@root/modal-renderer/src/composables/useAuth'
import {
  provideDownloadHandler,
  provideCloseWindowHandler,
} from '@root/modal-renderer/src/composables/useTaskDownload'
import { provideMagnetParser } from '@root/modal-renderer/src/composables/useMagnetParser'

// ==================== 类型定义 ====================
import {
  TaskParseStatus,
  EmuleTaskStatus,
  DownloadPathType,
  type TaskFileSelectionMap,
  type EmuleTaskInfo,
  type TaskSettings,
  type IEmuleUrlDetailInfo,
  type IP2spUrlDetailInfo,
  type INewTaskDataItem,
  type IUrlWithType,
  type IUrlWithTypeArray,
  type IUrlDataMap,
  type ITaskExtraDataType,
  type IOtherUrlDetailInfo,
  type DownloadEventParams,
  type TaskExtDataMap,
  type TaskError,
} from '@root/modal-renderer/types/new-task.type'

// ==================== 组件配置 ====================
const alertDialog = useAlertDialog()
const { initializeAuth, cleanupAuth } = useAuth()

// Props 定义
interface Props {
  options?:
    | ThunderNewTaskHelperNS.IShowNewTaskWindowOptions
    | (ThunderNewTaskHelperNS.IShowPreNewTaskWindowOptions & {
        taskData?: INewTaskDataItem[]
      })
}

const props = withDefaults(defineProps<Props>(), {
  options: () => ({}),
})

// ==================== 窗口位置管理 ====================
const { overridePosition, resizeToFitContent } = usePositionMixinComponent()
const defaultPositionOptions = {
  autoSize: true,
  show: false,
  windowWidth: 680,
  windowHeight: 316,
  relatePos: PopUpTypes.RelatePosType.CenterParent,
}

// ==================== 状态管理 Store ====================
const createTaskStore = useCreateTaskStore()
const downloadPathStore = useDownloadPathStore()
const taskSettingStore = useTaskSettingStore()
const downloadCloudPathStore = useDownloadCloudPathStore()
const userStore = useUserStore()

// ==================== Store 状态引用 ====================
const { isMergeTask, taskGroupName, currentUserCloudAddQuotas } = storeToRefs(createTaskStore)

// ==================== 响应式状态 ====================
// 窗口状态
const hasShownLoginDialog = ref(false)
const isWindowBlurred = ref(false)
const dialogVisible = ref(true)
const isLoading = ref(false)

// 任务数据状态
const allUrls = ref<string[]>([])
const allUrlsWithType = ref<IUrlWithTypeArray>([])
const dataMap = ref<IUrlDataMap>({})
const urlExtraDataMap = ref<ITaskExtraDataType>({})
const optionsExtData = ref<TaskExtDataMap>({})
const parsedTaskData = ref<INewTaskDataItem[]>([])

// 用户输入和选择状态
const inputValue = ref('')
const checkedFileIndexes = ref<TaskFileSelectionMap>({})
const downloadButtonRef = ref()

// 任务相关状态
const emuleTaskArray = ref<EmuleTaskInfo[]>([])
const duplicateTaskIds = ref<{ taskId: number; taskType: BaseType.TaskType; url: string }[]>([])
const duplicateTaskDetails = ref<any[]>([])
const showDuplicateTaskDialog = ref(false)

// 磁盘和媒体状态
const isDiskSpaceInsufficient = ref(false)
const videoList = ref<any[]>([])
const audioList = ref<any[]>([])

// ==================== 窗口焦点事件处理 ====================
const handleWindowBlur = () => {
  isWindowBlurred.value = true
}

const handleWindowFocus = () => {
  isWindowBlurred.value = false
}

const handleVisibilityChange = () => {
  isWindowBlurred.value = document.hidden
}

// ==================== 重复任务管理 ====================
const addDuplicateTaskId = (taskId: number, taskType: BaseType.TaskType, url: string): void => {
  if (!duplicateTaskIds.value.some(item => item.taskId === taskId)) {
    duplicateTaskIds.value.push({ taskId, taskType, url })
    console.log('添加重复任务信息:', { taskId, taskType, url })
  }
}

const clearDuplicateTaskUrls = (): void => {
  duplicateTaskIds.value = []
  console.log('清空重复任务列表')
}

// 新增：获取重复任务的下载详情
const getDuplicateTasksDownloadDetails = async (): Promise<any[]> => {
  console.log('获取重复任务的下载详情:', duplicateTaskIds.value)

  // 遍历所有duplicateTaskIds 直接使用存储的taskId和taskType
  const tempDuplicateTaskDetails: any[] = []

  try {
    for (const duplicateItem of duplicateTaskIds.value) {
      const { taskId, taskType, url } = duplicateItem

      try {
        // 通过taskId直接获取任务对象
        const existingTask = await TaskManager.GetInstance().findTaskById(taskId)
        console.log('existingTask', existingTask)
        if (existingTask) {
          // 获取任务基础信息
          const taskBase = await existingTask.getTaskBase()

          let taskDetail: any = {
            url: url,
            taskId: taskId,
            taskType: taskType,
            taskBase: taskBase,
          }

          // 添加 dataMap 中的详情信息
          const dataMapInfo = dataMap.value[url]
          if (dataMapInfo) {
            taskDetail.dataMapInfo = dataMapInfo
            console.log(`添加 dataMap 详情信息:`, dataMapInfo)
          }

          // 根据任务类型获取特定信息
          switch (taskType) {
            case BaseType.TaskType.Bt:
              // BT任务，获取BT特有信息
              try {
                const btTask = existingTask.toBtTask()
                const infoHash = await btTask.getInfoHash()
                const completeCount = await btTask.getCompleteCount()
                const downloadCount = await btTask.getDownloadCount()
                const totalCount = await btTask.getTotalCount()
                const btFileInfos = await btTask.getBtFileInfos()
                const btSubFileScheduler = await btTask.getBtSubFileScheduler()

                taskDetail.infoHash = infoHash
                taskDetail.btTaskInfo = {
                  infoHash: infoHash,
                  completeCount: completeCount,
                  downloadCount: downloadCount,
                  totalCount: totalCount,
                  btFileInfos: btFileInfos,
                  btSubFileScheduler: btSubFileScheduler,
                }
              } catch (error) {
                console.warn(`获取BT任务信息失败: ${error}`)
              }
              break

            case BaseType.TaskType.P2sp:
              // P2SP任务特有信息
              try {
                // const p2spTask = existingTask.toP2spTask()
                // // 可以获取更多P2SP特有信息
                // taskDetail.p2spTaskInfo = {
                //   // 添加P2SP特有字段
                // }
              } catch (error) {
                console.warn(`获取P2SP任务信息失败: ${error}`)
              }
              break

            case BaseType.TaskType.Emule:
              // Emule任务特有信息
              try {
                const emuleTask = existingTask.toEmuleTask()
                const fileHash = await emuleTask.getFileHash()
                taskDetail.fileHash = fileHash
                taskDetail.emuleTaskInfo = {
                  fileHash: fileHash,
                  // 可以添加更多Emule特有字段
                }
              } catch (error) {
                console.warn(`获取Emule任务信息失败: ${error}`)
              }
              break
          }

          tempDuplicateTaskDetails.push(taskDetail)
          console.log(`✅成功获取重复任务详情:`, taskDetail)
        } else {
          console.warn(`无法获取任务对象，taskId: ${taskId}`)
        }
      } catch (error) {
        console.error(`获取重复任务详情失败，taskId: ${taskId}`, error)
      }
    }

    console.log(
      `获取重复任务详情完成，共 ${tempDuplicateTaskDetails.length} 个任务:`,
      tempDuplicateTaskDetails
    )

    // 更新全局变量
    duplicateTaskDetails.value = tempDuplicateTaskDetails

    // 如果有重复任务，显示对话框
    if (tempDuplicateTaskDetails.length > 0) {
      showDuplicateTaskDialog.value = true
      showDuplicateTaskWindow()
    }

    return tempDuplicateTaskDetails
  } catch (error) {
    console.error('获取重复任务详情时出错:', error)
    return []
  }
}

// ==================== 计算属性 ====================
// 是否是自动创建任务
const isAutoCreateTask = computed(() => {
  return (
    props.options &&
    'taskData' in props.options &&
    props.options.taskData &&
    props.options.taskData.length > 0
  )
})

// 判断是否显示任务列表
const showTaskList = computed(() => {
  return allUrlsWithType.value && allUrlsWithType.value.length > 0
})

// 判断是否有有效的任务
const hasValidTasks = computed(() => {
  return allUrlsWithType.value.length > 0
})

// 判断是否有选中的文件
const hasSelectedFiles = computed(() => {
  return Object.values(checkedFileIndexes.value).some(
    selectionInfo => selectionInfo?.fileIndexes?.length > 0
  )
})

// 解析任务数据
const taskDataFromOptions = computed(() => {
  if (
    props.options &&
    'taskData' in props.options &&
    props.options.taskData &&
    Array.isArray(props.options.taskData)
  ) {
    console.log('[AutoCreateTaskView] Found taskData array with length:', props.options.taskData.length)
    return props.options.taskData
  }
  console.log('[AutoCreateTaskView] No valid taskData found, returning empty array')
  return []
})

// 当前任务保存路径
const taskSavePath = computed(() => {
  const path = downloadPathStore.getCurrentDownloadPath()
  console.log('[CreateTask] 当前任务保存路径:', path)
  return path || 'C:\\download'
})

// 从 options 中提取 selectedPathType
const selectedPathType = computed(() => {
  if (props.options && 'selectedPathType' in props.options) {
    return props.options.selectedPathType
  }
  return undefined
})

// 云添加剩余次数
const freeCloudAddCount = computed(() => {
  return currentUserCloudAddQuotas.value.limit - currentUserCloudAddQuotas.value.usage
})

// ==================== 组件事件 ====================
const emit = defineEmits(['close', 'confirm', 'cancel'])

// ==================== 初始化和Provide设置 ====================
// 初始化任务管理器
function initTaskManager() {
  TaskManager.GetInstance().init()
}

// 提供下载处理函数给所有子组件
provideDownloadHandler(handleAutoCreateDownload)
// 提供关闭窗口处理函数给所有子组件
provideCloseWindowHandler(closeWindow)
// 提供磁力解析函数给所有子组件
provideMagnetParser(parseMagnetAndDownloadTorrent)

// 初始化任务管理器
initTaskManager()

/**
 * 解析Thunder链接，返回真实URL
 * 参考 create-task/index.vue 中的 parseThunderUrlIfNeeded 函数
 * @param url 可能是Thunder链接的URL
 * @returns 解析后的真实URL，如果不是Thunder链接则返回原URL
 */
async function parseThunderUrlIfNeeded(url: string): Promise<string> {
  try {
    const isThunderUrl = await DkHelper.isThunderPrivateUrl(url)

    if (isThunderUrl) {
      console.log('[AutoCreateTaskView] 检测到Thunder链接，开始解析:', url)

      const thunderParseResult = await DkHelper.parseThunderPrivateUrl(url)

      if (thunderParseResult && thunderParseResult.trim()) {
        console.log('[AutoCreateTaskView] Thunder链接解析成功，解析后的URL:', thunderParseResult)
        return thunderParseResult.trim()
      } else {
        console.log('[AutoCreateTaskView] Thunder链接解析失败，返回原URL:', url)
        return url
      }
    }

    return url
  } catch (error) {
    console.error('[AutoCreateTaskView] 解析Thunder链接时出错，返回原URL:', error, 'URL:', url)
    return url
  }
}

/**
 * 处理单个URL，获取其真实URL和任务类型
 * 参考 create-task/index.vue 中 handleInputChangeDelay 函数的URL处理逻辑
 * @param url 原始URL
 * @returns 包含真实URL和任务类型的对象
 */
async function processUrlWithType(url: string): Promise<IUrlWithType> {
  try {
    // 首先尝试解析Thunder链接（如果是的话）
    const realUrl = await parseThunderUrlIfNeeded(url)

    // 然后获取真实URL的任务类型
    const taskType = await DkHelper.getTaskTypeFromUrl(realUrl)

    console.log('[AutoCreateTaskView] URL处理完成:', { originalUrl: url, realUrl, taskType })
    return { url: realUrl, taskType: taskType }
  } catch (error) {
    console.error('[AutoCreateTaskView] 处理URL失败:', url, error)
    // 如果处理失败，默认为P2sp类型
    return { url, taskType: BaseType.TaskType.P2sp }
  }
}

/**
 * 从taskData中提取所有URLs并获取其类型
 * 遍历taskData，提取每个任务的URL，并通过DkHelper获取任务类型
 */
async function extractUrlsFromTaskData() {
  console.log('[AutoCreateTaskView] extractUrlsFromTaskData called')

  const taskData = taskDataFromOptions.value

  if (!taskData || taskData.length === 0) {
    console.log('[AutoCreateTaskView] No task data to extract URLs from')
    allUrls.value = []
    allUrlsWithType.value = []
    return
  }

  console.log('[AutoCreateTaskView] Extracting URLs from task data:', taskData)

  // 第一步：提取所有URLs
  const extractedUrls: string[] = []

  taskData.forEach((task, index) => {
    console.log(`[AutoCreateTaskView] Processing task ${index} for URL extraction:`, task)

    // 从任务中提取URL
    let taskUrl = ''
    if (task.url) {
      taskUrl = task.url
    } else if (task.fileName && task.fileName.startsWith('http')) {
      // 有些情况下URL可能存储在fileName字段中
      taskUrl = task.fileName
    }

    if (taskUrl) {
      extractedUrls.push(taskUrl)
      console.log(`[AutoCreateTaskView] Extracted URL from task ${index}:`, taskUrl)
    } else {
      console.warn(`[AutoCreateTaskView] No URL found in task ${index}:`, task)
    }
  })

  // 临时存储提取的URLs，稍后在去重合并后统一更新allUrls
  console.log('[AutoCreateTaskView] All extracted URLs:', extractedUrls)

  // 第二步：异步处理URL，获取真实URL和类型
  if (extractedUrls.length > 0) {
    try {
      console.log(
        '[AutoCreateTaskView] Starting URL type detection for',
        extractedUrls.length,
        'URLs'
      )

      const urlsWithTypePromises = extractedUrls.map(async (url, index) => {
        console.log(`[AutoCreateTaskView] Processing URL ${index}:`, url)
        return await processUrlWithType(url)
      })

      const processedUrlsWithType = await Promise.all(urlsWithTypePromises)

      // 🔥 过滤掉taskType为Unknown的URL
      const newUrlsWithType = processedUrlsWithType.filter(item => {
        if (item.taskType === BaseType.TaskType.Unkown) {
          console.warn('过滤掉未知类型的URL:', item.url)
          return false
        }
        return true
      })

      // 🔥 直接使用新输入的URL数组，完全替换旧数据，不进行合并
      const mergedUrlsWithType = newUrlsWithType

      // 🔥 解析完成后，统一更新状态
      // 从合并后的数组中提取URL，保持与allUrlsWithType的同步
      allUrls.value = mergedUrlsWithType.map(item => item.url)
      allUrlsWithType.value = [...mergedUrlsWithType]

      console.log('所有URLs及其类型（Thunder已解析，已去重）:', allUrlsWithType.value)

      // 按类型分组显示统计信息
      const typeStats = allUrlsWithType.value.reduce(
        (stats, item) => {
          const typeName = BaseType.TaskType[item.taskType] || 'Unknown'
          stats[typeName] = (stats[typeName] || 0) + 1
          return stats
        },
        {} as Record<string, number>
      )

      console.log('[AutoCreateTaskView] URL type statistics:', typeStats)
    } catch (error) {
      console.error('[AutoCreateTaskView] 批量处理URL类型检测时出错:', error)
    }
  }
}

// 解析和验证任务数据
const parseTaskData = async () => {
  console.log('[AutoCreateTaskView] parseTaskData called')

  const taskData = taskDataFromOptions.value

  if (!taskData || taskData.length === 0) {
    console.log('[AutoCreateTaskView] No task data to parse')
    parsedTaskData.value = []
    return
  }

  console.log('[AutoCreateTaskView] Parsing task data:', taskData)

  // 验证和处理每个任务项
  const validTaskData: INewTaskDataItem[] = []

  taskData.forEach((task, index) => {
    console.log(`[AutoCreateTaskView] Processing task ${index}:`, task)

    // 基本验证
    if (!task) {
      console.warn(`[AutoCreateTaskView] Task ${index} is null or undefined`)
      return
    }

    // 确保必要的字段存在
    const processedTask: INewTaskDataItem = {
      ...task,
      // 如果没有 fileName，尝试从 url 提取
      fileName: task.fileName || extractFileNameFromUrlUtil(task.url) || `Task_${index + 1}`,
      // 确保 fileSize 是数字
      fileSize:
        typeof task.fileSize === 'number'
          ? task.fileSize
          : task.fileSize
            ? parseInt(String(task.fileSize))
            : 0,
      // 确保有默认的任务类型，但保持原有类型
      taskType: task.taskType,
    }

    console.log(`[AutoCreateTaskView] Processed task ${index}:`, processedTask)
    validTaskData.push(processedTask)
  })

  console.log('[AutoCreateTaskView] Final parsed task data:', validTaskData)
  parsedTaskData.value = validTaskData

  // 解析完任务数据后，提取URLs并获取类型
  await extractUrlsFromTaskData()

  // 处理 extData：如果 props.options.extData 存在，将对应 URL 的详情存储到独立的 extDataFromOptions 中
  if (props.options && 'extData' in props.options && props.options.extData) {
    console.log('[AutoCreateTaskView] Processing extData:', props.options.extData)

    // 将 extData 存储到独立的变量中，避免被覆盖
    optionsExtData.value = { ...props.options.extData }
    console.log('[AutoCreateTaskView] Stored extData to extDataFromOptions:', optionsExtData.value)
  }
}

// 监听isAutoCreateTask
watch(
  () => isAutoCreateTask.value,
  async newIsAutoCreateTask => {
    if (!newIsAutoCreateTask) {
      rendererWindowSize()
      console.log(
        'rendererWindowSize 33 isAutoCreateTask 变化: rendererWindowSize',
        newIsAutoCreateTask
      )
      setTimeout(() => {
        const currentWindow = PopUpNS.getCurrentWindow()
        console.log('🔍 [onMounted] 显示窗口:', currentWindow)
        if (currentWindow) {
          currentWindow.show()
        }
      }, 100)
    }
  },
  { immediate: true }
)

watch(
  () => props.options,
  async newOptions => {
    console.log('[AutoCreateTaskView] Options changed:', newOptions)
    await parseTaskData()
  },
  {
    deep: true,
    immediate: true,
  }
)

function rendererWindowSize() {
  nextTick(() => {
    overridePosition({
      ...defaultPositionOptions,
      windowHeight: 0,
      windowWidth: 0,
      selector: '.create-task-content',
    })
    resizeToFitContent()
  })
}

watch(
  allUrlsWithType,
  async newUrlsWithType => {
    console.log('allUrlsWithType 变化:', newUrlsWithType)

    if (!newUrlsWithType || newUrlsWithType.length === 0) {
      if (!isAutoCreateTask.value) {
        console.log(
          'rendererWindowSize 11 allUrlsWithType 变化: rendererWindowSize',
          newUrlsWithType
        )
        rendererWindowSize()
      }
      return
    }

    console.log(
      'allUrlsWithType 变化:',
      newUrlsWithType,
      'isAutoCreateTask',
      isAutoCreateTask.value
    )

    if (newUrlsWithType.length > 0 && !isAutoCreateTask.value) {
      console.log('rendererWindowSize 22 allUrlsWithType 变化: rendererWindowSize', newUrlsWithType)
      rendererWindowSize()
    }
    // 遍历所有URL，根据taskType决定调用哪个函数
    for (const urlWithType of newUrlsWithType) {
      const { url, taskType } = urlWithType

      try {
        // 使用通用处理函数，消除代码冗余 全部转移到 processUrlByTaskType 后可以删除 processUrlByType
        await processUrlByTaskType(url, taskType)
      } catch (error) {
        console.error(`处理URL时出错: ${url}`, error)
      }
    }
  },
  { deep: true, immediate: false }
)

watch(
  inputValue,
  newValue => {
    handleInputChangeDelay(newValue)
  },
  { immediate: false }
)

watch(
  dataMap,
  newValue => {
    console.log('create task  dataMap', newValue)
    // 当dataMap变化时，更新视频和音频列表
    const mediaLists = updateMediaLists(newValue)
    videoList.value = mediaLists.video
    audioList.value = mediaLists.audio
  },
  { deep: true, immediate: false }
)

/**
 * 清空所有任务相关状态
 * Clear all task-related states
 */
function clearAllTaskStates(): void {
  allUrls.value = []
  allUrlsWithType.value = []
  dataMap.value = {}
  urlExtraDataMap.value = {}
  clearDuplicateTaskUrls() // 新增：清空重复任务列表
  console.log('已清空所有任务状态')
}

async function handleInputChangeDelay(newValue: string) {
  console.log('文本内容变化:', newValue)

  // 🔥 如果输入为空，立即清空所有状态
  if (!newValue || !newValue.trim()) {
    clearAllTaskStates()
    return
  }

  // 🔥 输入不为空时，先解析新的URLs，不立即清空现有状态
  // 按回车切割输入内容
  let urls = parseMultilineUrls(newValue)

  // 过滤掉空的URL并去重，保持第一次出现的顺序
  const validUrls = urls
    .filter(url => url.trim()) // 过滤掉空的URL
    .map(url => url.trim()) // 去除前后空格
    .filter((url, index, array) => array.indexOf(url) === index) // 去重，保持第一次出现的顺序

  console.log('去重后的有效URLs:', validUrls)

  // ========== 异步处理URL（包含Thunder解析和类型识别） ==========
  try {
    const urlsWithTypePromises = validUrls.map(async url => {
      try {
        // 首先尝试解析Thunder链接（如果是的话）
        const realUrl = await parseThunderUrlIfNeeded(url)

        // 然后获取真实URL的任务类型
        const taskType = await DkHelper.getTaskTypeFromUrl(realUrl)

        console.log('handleInputChangeDelay', { taskType })
        return { url: realUrl, taskType }
      } catch (error) {
        console.error('处理URL失败:', url, error)
        // 如果处理失败，默认为P2sp类型
        return { url, taskType: BaseType.TaskType.P2sp }
      }
    })

    const processedUrlsWithType = await Promise.all(urlsWithTypePromises)

    // 🔥 过滤掉taskType为Unknown的URL
    const newUrlsWithType = processedUrlsWithType.filter(item => {
      if (item.taskType === BaseType.TaskType.Unkown) {
        console.warn('过滤掉未知类型的URL:', item.url)
        return false
      }
      return true
    })

    // 🔥 直接使用新输入的URL数组，完全替换旧数据，不进行合并
    const mergedUrlsWithType = newUrlsWithType

    // 🔥 解析完成后，统一更新状态
    // 从合并后的数组中提取URL，保持与allUrlsWithType的同步
    allUrls.value = mergedUrlsWithType.map(item => item.url)
    allUrlsWithType.value = [...mergedUrlsWithType]

    console.log('所有URLs及其类型（Thunder已解析，已去重）:', allUrlsWithType.value)

    // 🔥 清理不再需要的 dataMap 和 urlExtraDataMap 条目
    const currentUrls = new Set(mergedUrlsWithType.map(item => item.url))
    const newDataMap = {}
    const newUrlExtraDataMap = {}

    // 只保留当前URLs对应的数据
    for (const url of currentUrls) {
      if (dataMap.value[url]) {
        newDataMap[url] = dataMap.value[url]
      }
      if (urlExtraDataMap.value[url]) {
        newUrlExtraDataMap[url] = urlExtraDataMap.value[url]
      }
    }

    // 更新清理后的数据映射
    dataMap.value = newDataMap
    urlExtraDataMap.value = newUrlExtraDataMap
  } catch (error) {
    console.error('批量处理URL时出错:', error)
  }
}

/**
 * 通用URL处理函数
 * 根据URL类型调用相应的处理函数，体现函数式编程思想
 * @param url 待处理的URL
 * @param taskType URL类型
 */
async function processUrlByTaskType(url: string, taskType: BaseType.TaskType): Promise<void> {
  const trimmedUrl = url.trim()

  switch (taskType) {
    case BaseType.TaskType.Magnet:
      await parseMagnetAndDownloadTorrent({ url: trimmedUrl })
      break

    case BaseType.TaskType.P2sp:
      // p2sp任务和ftp任务返回的taskType都是P2sp，需要自动检测协议类型
      const protocol = isFtpUrl(trimmedUrl) ? 'FTP' : 'HTTP'
      await getP2spLinkInfo({ url: trimmedUrl, protocol })
      break

    case BaseType.TaskType.Emule:
      await getEmuleTaskInfo({ url: trimmedUrl })
      break

    case BaseType.TaskType.Bt:
      console.log('检测到BT种子文件:', trimmedUrl)
      // 如果是本地种子文件路径
      if (trimmedUrl.endsWith('.torrent')) {
        // await createBtTask(trimmedUrl);
        console.log('种子文件处理逻辑待实现')
      }
      break

    default:
      console.warn('未知的URL类型，不处理:', trimmedUrl, taskType)
      // 对于未知类型，默认尝试作为HTTP链接处理
      // await getP2spLinkInfo({ url: trimmedUrl, protocol: 'HTTP' })
      break
  }
}

// 添加P2SP链接到数组的统一函数（合并HTTP和FTP处理）
/**
 * 获取P2SP链接详细信息并存储到dataMap
 * Get P2SP link detailed information and store it in dataMap
 *
 * @param params 包含url、协议类型和任务类型的参数对象
 * @returns Promise<IP2spUrlDetailInfo | null> 成功时返回P2SP任务详细信息对象，失败时返回null
 */
async function getP2spLinkInfo(params: {
  url: string
  protocol?: 'HTTP' | 'FTP'
}): Promise<IP2spUrlDetailInfo | null> {
  // 检查是否已存在相同的URL
  const url = params.url
  const protocol = params.protocol || 'HTTP'
  // const taskType = BaseType.TaskType.P2sp
  // 判断data map中是否有当前链接的详细信息
  const exists = dataMap.value[url]
  if (exists) {
    console.log(`${protocol}链接已存在，跳过:`, url)
    return exists as IP2spUrlDetailInfo
  }

  try {
    // 并行调用两个解析函数，确保性能不受影响
    console.log(`开始解析${protocol} P2SP URL:`, url)

    // 创建初始的P2SP任务信息，显示解析中状态
    const fileType = detectFileType(url)
    const fileName = extractFileNameFromUrlUtil(url)

    // 先存储解析中的状态到 dataMap，符合 IP2spUrlDetailInfo 接口
    const initialP2spInfo: IP2spUrlDetailInfo = {
      url,
      fileType,
      fileName: fileName || '解析中...',
      addTime: Date.now(),
      protocol,
      taskType: 1 as const, // TaskType.P2sp
      detail: null,
      fileSize: 0,
      // P2SP 特有字段初始值
      fullPath: '',
      hostName: '',
      password: '',
      port: 443,
      schema: 'https://',
      userName: '',
    }

    dataMap.value[url] = initialP2spInfo

    // 主要的解析任务 - 这个是必须等待的
    const parseP2spPromise = DkHelper.parseP2spUrl(url)
    // 远程URL数据获取 - 这个可以异步进行，不阻塞主流程
    const remoteDataPromise = getRemoteUrlDataInfo(url)

    // 等待主要解析完成
    const parseResult = await parseP2spPromise

    if (parseResult) {
      console.log(`${protocol} P2SP URL解析成功:`, url, '解析结果:', parseResult)

      // 等待远程数据获取完成（如果还没完成的话）
      const remoteData = await remoteDataPromise

      // 提取 P2SP 特定字段
      const p2spFields = extractP2spFields(parseResult, remoteData)

      console.log('p2spFields', p2spFields, remoteData)
      // 创建并存储 P2SP 数据项，符合 IP2spUrlDetailInfo 接口
      const successP2spInfo: IP2spUrlDetailInfo = {
        url,
        fileType,
        fileName: fileName || extractFileNameFromUrlUtil(url) || 'unknown-file',
        addTime: Date.now(),
        protocol,
        taskType: 1 as const, // TaskType.P2sp
        detail: parseResult,
        fileSize: remoteData.fileSize || 0,

        // P2SP 特有字段
        fullPath: parseResult?.fullPath || '',
        hostName: parseResult?.hostName || '',
        password: parseResult?.password || '',
        port: parseResult?.port || 443,
        schema: parseResult?.schema || 'https://',
        userName: parseResult?.userName || '',

        // 从远程数据中提取的额外信息
        ...(remoteData.gcid && { gcid: remoteData.gcid }),
        ...(remoteData.result && { remoteResult: remoteData.result }),
      }

      dataMap.value[url] = successP2spInfo

      console.log(
        `${url} P2SP URL 详情:`,
        dataMap.value[url],
        '所有文件详情',
        dataMap.value,
        '接口返回的详情',
        { ...remoteData }
      )
      return successP2spInfo
    } else {
      console.log(`${protocol} P2SP URL解析失败，返回结果为空:`, url)

      // 解析失败，更新信息
      const failedP2spInfo: IP2spUrlDetailInfo = {
        ...initialP2spInfo,
        fileName: 'P2SP链接解析失败',
        detail: null,
      }

      dataMap.value[url] = failedP2spInfo
      return null
    }
  } catch (error) {
    console.error(`解析${protocol} P2SP URL时出错:`, error, 'URL:', url)

    // 解析出错，更新信息
    if (dataMap.value[url]) {
      const errorP2spInfo: IP2spUrlDetailInfo = {
        ...(dataMap.value[url] as IP2spUrlDetailInfo),
        fileName: 'P2SP链接解析出错',
        detail: null,
      }

      dataMap.value[url] = errorP2spInfo
    }
    return null
  }
}

/**
 * 安全地获取远程URL数据信息
 * 即使失败也不会影响主流程
 * @param url
 * @returns
 */
async function getRemoteUrlDataInfo(url: string): Promise<ThunderNewTaskHelperNS.IResponseP2sp> {
  try {
    return await ThunderNewTaskHelperNS.getRemoteUrlDataInfo(url)
  } catch (error) {
    console.warn('获取远程URL数据信息失败，使用默认值:', error)
    return {}
  }
}

/**
 * 提取 P2SP 特定字段
 * 这是一个纯函数，便于测试和复用
 * @param parseResult
 * @param remoteData
 * @returns
 */
function extractP2spFields(parseResult: any, remoteData: ThunderNewTaskHelperNS.IResponseP2sp) {
  console.log('remoteData', remoteData)
  return {
    // 从 parseResult 中提取字段（基于 P2spUrlParseResult 接口）
    fullPath: parseResult?.fullPath || '',
    hostName: parseResult?.hostName || '',
    password: parseResult?.password || '',
    port: parseResult?.port || 443,
    schema: parseResult?.schema || 'https://',
    userName: parseResult?.userName || '',

    // 从远程数据中提取额外信息
    ...(remoteData.fileSize && { fileSize: remoteData.fileSize }),
    ...(remoteData.gcid && { gcid: remoteData.gcid }),
    ...(remoteData.result && { remoteResult: remoteData.result }),
  }
}



function handleDialogClose() {
  closeWindow('dialog_closed')
}

/**
 * 关闭当前弹窗
 */
async function closeWindow(scene?: string): Promise<void> {
  try {
    console.log('🔄 弹窗关闭场景:', !scene)
    // 检查是否传递了scene参数
    if (!scene || !scene.trim()) {
      console.error('❌ 关闭窗口失败：场景编号为空')
      return
    }
    const currentWindow = PopUpNS.getCurrentWindow()
    await currentWindow.close()
    console.log('✅ 弹窗已关闭，场景:', scene)
  } catch (error) {
    console.warn('❌ 关闭弹窗失败:', error, scene)
  }
}

/**
 * 获取任务的显示名称（合并了getFileNameFromInfo和getFinalTaskName的功能）
 * 优先级：自定义任务名称 > optionsExtData中的fileName > dataMap中的文件名/title > URL提取的文件名
 * @param url 任务URL
 * @param dataInfo 可选的任务数据对象，如果不提供则从dataMap中获取
 * @returns 最终的任务名称
 */
function getTaskDisplayName(url: string, dataInfo?: any): string {
  // 1. 优先使用自定义任务名称
  const customTaskName = taskSettingStore.getCustomTaskName(url)
  if (customTaskName) {
    console.log(`[CreateTask] 使用自定义任务名称: ${customTaskName}`)
    return customTaskName
  }

  // 2. 优先使用 optionsExtData 中的 fileName
  const optionsExtDataInfo = optionsExtData.value[url]
  if (optionsExtDataInfo && optionsExtDataInfo.fileName) {
    console.log(`[CreateTask] 使用 optionsExtData 中的文件名: ${optionsExtDataInfo.fileName}`)
    return optionsExtDataInfo.fileName
  }

  // 3. 使用传入的 dataInfo 或从 dataMap 中获取
  const taskDataInfo = dataInfo || dataMap.value[url]
  if (taskDataInfo) {
    // 磁力链任务的名称在title里，其他任务的在fileName里
    const fileName = taskDataInfo.fileName || taskDataInfo.title
    if (fileName) {
      console.log(`[CreateTask] 使用任务数据中的文件名: ${fileName}`)
      return fileName
    }
  }

  // 4. 最后使用从 URL 提取的文件名
  const urlFileName = extractFileNameFromUrlUtil(url) || url.split('/').pop() || 'unknown-file'
  console.log(`[CreateTask] 使用 URL 提取的文件名: ${urlFileName}`)
  return urlFileName
}



/**
 * 检查本地磁盘空间是否足够存储选中的文件
 * 通过遍历checkedFileIndexes获取选中文件的总大小，通过downloadPathStore的pathInfoList获取当前路径的剩余空间大小，
 * 判断文件总大小是否大于本地路径的剩余空间，如果大于，直接提示空间不足
 * @returns Promise<boolean> 返回true表示空间足够，false表示空间不足
 */
async function checkLocalDiskSpace(): Promise<boolean> {
  try {
    console.log('[CreateTask] 开始检查本地磁盘空间是否足够')

    // 使用已有的函数获取选中文件的总大小
    const totalSelectedFileSize = getSelectedFilesTotalSize()

    // 从downloadPathStore获取当前路径的剩余空间信息
    const currentPath = taskSavePath.value

    // 从currentPath 获取是哪个磁盘 比如c:\\迅雷下载 ， 那就是c盘， 通过调用
    const pathInfo = downloadPathStore.findLocalPanPathInfoByDir(currentPath)

    console.log('pathInfo', pathInfo)

    if (!pathInfo || !pathInfo.spaceInfo) {
      console.error('[CreateTask] 无法获取当前路径的空间信息:', currentPath)
      // 如果无法获取空间信息，为了安全起见，提示空间不足
      XMPMessage({
        message: '无法获取本地磁盘空间信息，请稍后重试',
        type: 'warning',
      })
      return false
    }

    const { free: availableSpace } = pathInfo.spaceInfo
    console.log(`[CreateTask] 本地磁盘剩余空间: ${availableSpace} 字节`)

    // 比较文件总大小和本地磁盘剩余空间
    if (totalSelectedFileSize > availableSpace) {
      console.warn(
        `[CreateTask] 本地磁盘空间不足: 需要 ${totalSelectedFileSize} 字节，剩余 ${availableSpace} 字节`
      )

      const formattedTotalSize = FileOperationHelper.formatSize(totalSelectedFileSize)
      const formattedAvailableSpace = FileOperationHelper.formatSize(availableSpace)

      const driveSpaceInfo = getLogicalDriveList()
      console.log(
        '[CreateTask] 驱动器空间信息:',
        `本地磁盘空间不足，选中文件总大小 ${formattedTotalSize}，磁盘剩余空间 ${formattedAvailableSpace}`,
        driveSpaceInfo
      )

      isDiskSpaceInsufficient.value = true

      // showDriveSpaceDialog({
      //   driveSpaceInfo,
      //   currentSavePath: taskSavePath.value,
      //   requiredSpace: formattedTotalSize,
      // })

      return false
    }

    console.log('[CreateTask] 本地磁盘空间充足，可以继续操作')
    isDiskSpaceInsufficient.value = false
    return true
  } catch (error) {
    console.error('[CreateTask] 检查本地磁盘空间时出错:', error)
    // 如果检查过程中出错，为了安全起见，提示空间不足
    XMPMessage({
      message: '检查本地磁盘空间时出错，请稍后重试',
      type: 'error',
      duration: 2000,
    })
    return false
  }
}

/**
 * 检查磁力链文件数量是否超出限制
 * 统计所有选中的磁力链任务中的文件数量，如果超过1000个文件则提示超出限制
 * @returns boolean 返回true表示文件数量在限制内，false表示超出限制
 */
function checkMagnetFileCountLimit(): boolean {
  try {
    console.log('[CreateTask] 开始检查磁力链文件数量限制')

    // 使用 getMagnetCount 函数获取磁力链文件数量
    const totalMagnetFileCount = getMagnetCount()

    // 检查是否超出最大文件数量的限制
    if (totalMagnetFileCount > MAX_MAGNET_FILE_COUNT) {
      console.warn(
        `[CreateTask] 磁力链文件数量超出限制: ${totalMagnetFileCount} > ${MAX_MAGNET_FILE_COUNT}`
      )
      showOverLimitAlert()

      return false
    }

    console.log('[CreateTask] 磁力链文件数量在限制内，可以继续操作')
    return true
  } catch (error) {
    console.error('[CreateTask] 检查磁力链文件数量限制时出错:', error)
    // 如果检查过程中出错，为了安全起见，提示超出限制
    showOverLimitAlert()
    return false
  }
}

// 函数-超出提醒
function showOverLimitAlert() {
  alertDialog.open({
    title: '单次任务上限提醒',
    content: `单次最多添加 ${MAX_MAGNET_FILE_COUNT} 个任务，超出部分请分批提交`,
    confirmText: '我知道了',
    showCancel: false,
    showCloseButton: true,
    variant: 'thunder',
    showTitleIcon: true,
    alwaysOnTop: true,
  })
}

/**
 * 获取已选中文件的统计信息
 * 统计磁力链类型个数、直链个数和总文件个数
 * @returns { magnetCount: number, directLinkCount: number, totalFileCount: number, magnetFileCount: number } 返回统计信息对象
 */
function getSelectedFilesStatistics(): {
  magnetCount: number
  directLinkCount: number
  totalFileCount: number
  magnetFileCount: number
} {
  try {
    console.log('[CreateTask] 开始获取已选中文件的统计信息')

    let magnetCount = 0 // 磁力链类型个数
    let directLinkCount = 0 // 直链个数（P2SP类型）
    let totalFileCount = 0 // 总文件个数
    let magnetFileCount = 0 // 磁力链文件个数

    // 遍历所有选中的任务
    for (const [url, selectionInfo] of Object.entries(checkedFileIndexes.value)) {
      if (!selectionInfo?.fileIndexes?.length) {
        continue // 跳过没有选中文件的任务
      }

      // 查找对应的 URL 类型信息
      const urlWithType = allUrlsWithType.value.find(item => item.url === url)
      if (!urlWithType) {
        console.warn(`[CreateTask] 未找到URL对应的任务类型信息: ${url}`)
        continue
      }

      const { taskType } = urlWithType
      const selectedFileCount = selectionInfo.fileIndexes.length

      // 根据任务类型进行分类统计
      switch (taskType) {
        case BaseType.TaskType.Magnet:
          // 磁力链类型：统计任务个数
          magnetCount++
          // 磁力链的文件个数累加到总文件个数和磁力链文件个数
          totalFileCount += selectedFileCount
          magnetFileCount += selectedFileCount
          console.log(`[CreateTask] 磁力链任务 ${url} 选中文件数量: ${selectedFileCount}`)
          break

        case BaseType.TaskType.P2sp:
          // 直链类型（P2SP）：统计任务个数
          directLinkCount++
          // P2SP任务通常只有一个文件，但也要累加到总文件个数
          totalFileCount += selectedFileCount
          console.log(`[CreateTask] 直链任务 ${url} 选中文件数量: ${selectedFileCount}`)
          break

        case BaseType.TaskType.Emule:
          // Emule类型：统计任务个数（也归类为直链）
          directLinkCount++
          // Emule任务通常只有一个文件，但也要累加到总文件个数
          totalFileCount += selectedFileCount
          console.log(`[CreateTask] Emule任务 ${url} 选中文件数量: ${selectedFileCount}`)
          break

        case BaseType.TaskType.Bt:
          // BT类型：统计任务个数（也归类为直链）
          directLinkCount++
          // BT任务的文件个数累加到总文件个数
          totalFileCount += selectedFileCount
          console.log(`[CreateTask] BT任务 ${url} 选中文件数量: ${selectedFileCount}`)
          break

        default:
          // 其他类型：统计任务个数（也归类为直链）
          directLinkCount++
          totalFileCount += selectedFileCount
          console.log(
            `[CreateTask] 其他类型任务 ${url} (${taskType}) 选中文件数量: ${selectedFileCount}`
          )
          break
      }
    }

    const statistics = {
      magnetCount,
      directLinkCount,
      totalFileCount,
      magnetFileCount,
    }

    console.log('[CreateTask] 文件统计信息:', statistics)
    return statistics
  } catch (error) {
    console.error('[CreateTask] 获取已选中文件统计信息时出错:', error)
    // 返回默认值
    return {
      magnetCount: 0,
      directLinkCount: 0,
      totalFileCount: 0,
      magnetFileCount: 0,
    }
  }
}

/**
 * 获取磁力链文件数量
 * 专门统计所有选中的磁力链任务中的文件数量
 * @returns number 返回磁力链文件总数
 */
function getMagnetCount(): number {
  try {
    const statistics = getSelectedFilesStatistics()

    // 从统计信息中提取磁力链文件数量
    const totalMagnetCount = statistics.magnetCount
    return totalMagnetCount
  } catch (error) {
    console.error('[CreateTask] 获取磁力链文件数量时出错:', error)
    return 0
  }
}

function handleDownload() {
  console.log('点击下载按钮')
  isDiskSpaceInsufficient.value = false
  handleConfirm()
}

//  下载选中的磁力链，需要获取选中的磁力链文件的下标index
const handleConfirm = async () => {
  console.log('开始处理任务确认，合并为任务组:', isMergeTask.value)

  try {
    // 检查磁力链文件数量是否超出限制
    const isMagnetFileCountValid = checkMagnetFileCountLimit()
    if (!isMagnetFileCountValid) {
      console.log('[CreateTask] 磁力链文件数量超出限制，取消下载操作')
      return
    }

    // 首先检查本地磁盘空间是否足够
    const hasEnoughSpace = await checkLocalDiskSpace()
    if (!hasEnoughSpace) {
      console.log('[CreateTask] 本地磁盘空间不足，取消下载操作')
      return
    }

    // 判断是否合并为任务组
    if (isMergeTask.value) {
      // 合并为任务组的逻辑
      await handleGroupTaskConfirm()
      // 任务组创建逻辑比较复杂，暂时保持原有的成功提示逻辑
      console.log('toast 任务成功2')
      closeWindow('task_confirmed')
      // showMessage('创建任务成功', 'success', 'task_confirmed')
    } else {
      // 分别处理每个任务的逻辑
      const result = await handleIndividualTasksConfirm()

      // 使用专门的函数处理任务创建结果
      await processIndividualTasksResult(result)
    }

    // 创建任务完成后记录下载路径到历史记录
    await recordDownloadPath(taskSavePath.value, DownloadPathType.Local)

    savePendingAllTasks()
  } catch (error) {
    console.error('创建任务过程中出错:', error)
    // 即使出错也尝试记录路径
    await recordDownloadPath(taskSavePath.value, DownloadPathType.Local)
  }
}

/**
 * 处理个别任务创建的结果
 * @param result handleIndividualTasksConfirm 的返回结果
 * @returns Promise<boolean> 是否全部任务成功
 */
async function processIndividualTasksResult(result: {
  success: boolean
  successCount: number
  failureCount: number
  duplicateCount: number
  totalCount: number
  errors: Array<{ url: string; taskType: BaseType.TaskType; error: Error }>
}): Promise<boolean> {
  let allTasksSuccessful = true

  // 检查是否存在重复任务错误，需要显示重复任务对话框
  const hasDuplicateTaskErrors = result.errors.some(error => {
    // 使用新的 TaskError 类型进行判断，更可靠
    const taskError = error.error as TaskError
    return taskError.type === 'existed'
  })

  // 如果有重复任务错误，获取重复任务详情并显示对话框
  if (hasDuplicateTaskErrors || duplicateTaskIds.value.length > 0) {
    console.log('检测到重复任务，准备显示重复任务对话框')
    await getDuplicateTasksDownloadDetails()
    // getDuplicateTasksDownloadDetails 内部会设置 showDuplicateTaskDialog.value = true
    return false // 有重复任务时不显示成功提示
  }

  // 处理任务创建结果并显示相应提示
  if (result.success && result.successCount > 0) {
    // 全部成功
    console.log(`成功创建 ${result.successCount} 个任务`)
    allTasksSuccessful = true
  } else if (result.successCount > 0 && result.failureCount > 0) {
    // 部分成功
    allTasksSuccessful = false
    console.warn(`部分任务创建失败 - 成功: ${result.successCount}, 失败: ${result.failureCount}`)
    XMPMessage({
      message: `${result.successCount} 个任务创建成功，${result.failureCount} 个任务创建失败`,
      type: 'warning',
    })
  } else if (result.failureCount > 0) {
    // 全部失败
    allTasksSuccessful = false
    console.error(`所有任务创建失败 - 失败数量: ${result.failureCount}`)
    XMPMessage({
      message: `任务创建失败`,
      type: 'error',
    })
  } else if (result.totalCount === 0) {
    // 没有任务需要创建
    allTasksSuccessful = false
    console.warn('没有选中的任务需要创建')
    XMPMessage({
      message: '请先选择要下载的任务',
      type: 'warning',
    })
  }

  // 如果所有任务都成功，显示成功提示
  if (allTasksSuccessful) {
    closeWindow('task_confirmed')
  }

  return allTasksSuccessful
}

// 处理合并为任务组的逻辑
async function handleGroupTaskConfirm() {
  const subTasks: BaseType.NewGroupTaskInfo[] = []

  // 优化：直接遍历 checkedFileIndexes，只处理用户选中的任务
  for (const [url, selectionInfo] of Object.entries(checkedFileIndexes.value)) {
    // 检查是否有选中的文件
    if (!selectionInfo?.fileIndexes?.length) {
      continue
    }

    // 查找对应的 URL 类型信息
    const urlWithType = allUrlsWithType.value.find(item => item.url === url)
    if (!urlWithType) {
      console.warn(`未找到URL对应的任务类型信息: ${url}`)
      continue
    }

    const { taskType } = urlWithType
    const dataInfo = dataMap.value[url]

    if (!dataInfo) {
      console.warn(`未找到URL对应的数据信息: ${url}`)
      continue
    }

    console.log(url, 'dataInfo', dataInfo)

    // 处理磁力链
    if (taskType === BaseType.TaskType.Magnet) {
      const { status, torrentPath } = urlExtraDataMap.value[url]
      if (status === 'success') {
        // 使用任务对象中存储的torrent文件路径
        const seedFilePath = torrentPath

        if (!seedFilePath) {
          console.error(`磁力任务缺少torrent文件路径: ${dataInfo.fileName}`, dataInfo)
          continue // 跳过这个任务
        }

        const btTask: BaseType.NewGroupTaskInfo = {
          baseInfo: {
            background: false,
            taskType: BaseType.TaskType.Bt,
            taskBaseInfo: {
              savePath: taskSavePath.value,
              taskName: getTaskDisplayName(url, dataInfo) || generateDefaultMagnetTaskName(url),
            },
          },
          taskType: BaseType.TaskType.Bt,
          btInfo: {
            origin: url,
            seedFile: seedFilePath, // 使用任务对象中的torrent文件路径
            displayName: getTaskDisplayName(url, dataInfo) || generateDefaultMagnetTaskName(url),
            fileRealIndexLists: selectionInfo.fileIndexes,
            fileLists: dataInfo.detail?.fileLists || [],
            infoId: dataInfo.infoId || '',
            tracker: dataInfo.detail?.trackerUrls?.join(',') || '',
            subFileScheduler: createTaskStore.getSubFileSchedulerType(),
          },
        }
        subTasks.push(btTask)
        console.log('添加磁力任务到任务组:', btTask)
      }
    }

    // 处理p2sp
    if (taskType === BaseType.TaskType.P2sp) {
      // 获取任务设置
      const taskKey = dataInfo.infoId || dataInfo.taskId || dataInfo.id || dataInfo.url
      const taskSettings = getTaskSettings(taskKey)

      const p2spTaskInfo: BaseType.NewGroupTaskInfo = {
        baseInfo: {
          background: false,
          taskType: BaseType.TaskType.P2sp,
          taskBaseInfo: {
            savePath: taskSavePath.value,
            taskName: getTaskDisplayName(url, dataInfo),
            // 应用任务设置中的下载完成后打开设置
            openOnComplete: taskSettings.openAfterDownload ? 1 : 0,
          },
        },
        taskType: BaseType.TaskType.P2sp,
        p2spInfo: {
          url: url,
          // 应用任务设置到 p2spInfo
          refUrl: taskSettings.downloadUrl || url, // 使用设置中的下载链接或原始链接
          useOriginResourceOnly: taskSettings.downloadFromOriginalOnly,
          originResourceThreadCount: taskSettings.threadCount,
          loginFtp: taskSettings.loginFtpServer,
          ftpUserName: taskSettings.ftpUsername,
          ftpPassword: taskSettings.ftpPassword,
          origin: '',
        },
      }
      subTasks.push(p2spTaskInfo)
      console.log('添加P2SP任务到任务组（包含设置）:', p2spTaskInfo)
    }

    // 处理emule等等
    if (taskType === BaseType.TaskType.Emule) {
      // 检查Emule任务是否解析成功
      const emuleDetail = dataInfo.detail
      if (emuleDetail && emuleDetail.fileName && emuleDetail.fileHash) {
        const emuleTaskInfo: BaseType.NewGroupTaskInfo = {
          baseInfo: {
            background: false,
            taskType: BaseType.TaskType.Emule,
            taskBaseInfo: {
              savePath: taskSavePath.value,
              taskName: getTaskDisplayName(url, dataInfo),
            },
          },
          taskType: BaseType.TaskType.Emule,
          emuleInfo: {
            url: url,
          },
        }
        subTasks.push(emuleTaskInfo)
        console.log('添加Emule任务到任务组:', emuleTaskInfo)
      }
    }
  }

  if (subTasks.length > 0) {
    // 创建任务组
    console.log(
      '创建任务组，包含任务数量:',
      subTasks.length,
      'taskSavePath.value',
      taskSavePath.value
    )

    // 优先使用用户设置的任务组名称，如果不存在则使用默认名称
    const finalTaskName =
      taskGroupName.value && taskGroupName.value.trim()
        ? taskGroupName.value.trim()
        : `下载任务组_${Date.now()}`

    await createGroupTask({
      savePath: taskSavePath.value,
      taskName: finalTaskName,
      background: false,
      subTasks: subTasks,
    })
  } else {
    console.warn('没有选中的任务可以创建任务组')
  }
}

// 增加一个方法获取url对应的checkedIndex
function getCheckedFileIndexedByUrl({ url }) {
  // 更健壮地获取选中的文件索引
  let fileIndexes: number[] = []

  // 安全检查：确保 checkedFileIndexes.value[url] 存在
  console.log('checkedFileIndexes', checkedFileIndexes.value)
  const urlCheckedData = checkedFileIndexes.value[url]
  if (urlCheckedData && urlCheckedData.fileIndexes && Array.isArray(urlCheckedData.fileIndexes)) {
    fileIndexes = urlCheckedData.fileIndexes
  } else {
    console.warn(`未找到URL的选中文件索引数据: ${url}`)
    fileIndexes = []
  }

  return fileIndexes
}

// ==================== 任务创建相关函数 ====================

/**
 * 创建单个任务的Promise
 */
function createTaskPromise(urlWithType: IUrlWithType): Promise<any> | null {
  const { url, taskType } = urlWithType
  const dataInfo = dataMap.value[url]

  switch (taskType) {
    case BaseType.TaskType.Magnet:
      const { status, torrentPath } = urlExtraDataMap.value[url]
      if (status === 'success') {
        const selectedFiles = getCheckedFileIndexedByUrl({ url })
        return createBtTaskFromTorrent(torrentPath, url, selectedFiles)
      }
      break

    case BaseType.TaskType.P2sp:
      const isP2spSelected = checkedFileIndexes.value[url]?.fileIndexes?.length > 0
      if (isP2spSelected) {
        const taskSettings = getTaskSettings(url)
        return createP2spTask(url, taskSettings)
      }
      break

    case BaseType.TaskType.Emule:
      const isEmuleSelected = checkedFileIndexes.value[url]?.fileIndexes?.length > 0
      if (isEmuleSelected) {
        return createEmuleTask(dataInfo as EmuleTaskInfo)
      }
      break
  }

  return null
}

/**
 * 处理分别创建各个任务的确认逻辑
 */
async function handleIndividualTasksConfirm(): Promise<{
  success: boolean
  successCount: number
  failureCount: number
  duplicateCount: number
  totalCount: number
  errors: Array<{ url: string; taskType: BaseType.TaskType; error: Error }>
}> {
  // 创建所有任务的Promise
  const taskPromises = allUrlsWithType.value
    .map(urlWithType => {
      const taskPromise = createTaskPromise(urlWithType)
      return taskPromise ? {
        url: urlWithType.url,
        taskType: urlWithType.taskType,
        promise: taskPromise,
      } : null
    })
    .filter(Boolean) as Array<{
      url: string
      taskType: BaseType.TaskType
      promise: Promise<any>
    }>

  // 第二步：并行执行所有任务创建，使用 Promise.allSettled 确保所有任务都会被处理
  const results = await Promise.allSettled(taskPromises.map(item => item.promise))

  // 第三步：处理结果，收集错误信息
  const errors: { url: string; taskType: BaseType.TaskType; error: Error }[] = []
  let duplicateCount = 0

  results.forEach((result, index) => {
    const { url, taskType } = taskPromises[index]

    if (result.status === 'rejected') {
      const error = result.reason
      console.error(`创建任务失败 (URL: ${url}, 类型: ${taskType}):`, error)

      // 检查是否是重复任务错误
      const taskError = error as TaskError
      if (taskError.type === 'existed') {
        console.log(`跳过重复任务: ${url} (${taskError.taskType})`)
        duplicateCount++
        return // 重复任务不算错误
      }

      // 收集其他类型的错误
      console.warn(`任务创建出错: ${url}`, error)
      errors.push({
        url,
        taskType,
        error: error instanceof Error ? error : new Error(String(error)),
      })
    } else {
      console.log(`✅ 任务创建成功 (URL: ${url}, 类型: ${taskType})`)
    }
  })

  // 第四步：计算统计信息
  const totalCount = taskPromises.length
  const successCount = results.filter(result => result.status === 'fulfilled').length
  const failureCount = errors.length
  const success = errors.length === 0 // 只有没有真正的错误才算成功

  console.log(
    `任务创建完成 - 成功: ${successCount}, 失败: ${failureCount}, 重复跳过: ${duplicateCount}`
  )

  if (errors.length > 0) {
    console.warn(`创建任务过程中发生 ${errors.length} 个错误:`, errors)
  }

  // 返回执行结果
  return {
    success,
    successCount,
    failureCount,
    duplicateCount,
    totalCount,
    errors,
  }
}

/**
 * 从磁力任务信息创建BT任务
 */
// async function createBtTaskFromMagnetInfo(magnetTask: any, selectedFileIndexes: number[]) {
//   console.log('从磁力任务信息创建BT任务:', magnetTask.title, selectedFileIndexes)

//   try {
//     const btTask = await TaskManager.GetInstance().createTask({
//       taskInfo: {
//         background: false,
//         taskType: BaseType.TaskType.Bt,
//         taskBaseInfo: {
//           savePath: taskSavePath.value,
//           taskName: magnetTask.title || `磁力任务_${magnetTask.infoId}`,
//         },
//       },
//       btInfo: {
//         origin: magnetTask.url || '',
//         seedFile: '', // 如果有种子文件路径，需要设置
//         displayName: magnetTask.title || `磁力任务_${magnetTask.infoId}`,
//         fileRealIndexLists: selectedFileIndexes,
//         fileLists: magnetTask.fileLists,
//         infoId: magnetTask.infoId || '',
//         tracker: '',
//       },
//     })

//     if (btTask) {
//       await btTask.start()
//       console.log('BT任务创建并启动成功:', btTask.getId())
//     }
//   } catch (error) {
//     console.error('创建BT任务失败:', error)
//   }
// }

/**
 * 应用任务设置到P2SP任务参数
 * Apply task settings to P2SP task parameters
 */
// function applySettingsToP2spTask(p2spTask: any, settings: TaskSettings): void {
//   try {
//     // 如果是P2SP任务对象，应用HTTP头设置
//     if (p2spTask && typeof p2spTask.toP2spTask === 'function') {
//       const p2spTaskObj = p2spTask.toP2spTask()

//       // 应用FTP设置
//       if (settings.loginFtpServer && settings.ftpUsername) {
//         // 这里可以添加FTP认证逻辑
//         console.log('应用FTP设置:', settings.ftpUsername)
//       }

//       // 应用线程数设置
//       if (settings.threadCount > 0) {
//         // 这里可以设置下载线程数
//         console.log('设置下载线程数:', settings.threadCount)
//       }
//     }
//   } catch (error) {
//     console.error('应用P2SP任务设置失败:', error)
//   }
// }



// ==================== 磁力链接处理相关函数 ====================

/**
 * 检查磁力链接是否已存在
 */
function checkMagnetLinkExists(url: string, forceReparse: boolean): boolean {
  if (forceReparse) {
    console.log(`🔄 强制重新解析磁力链接: ${url}`)
    return false
  }

  const exists = dataMap.value[url]
  if (exists) {
    console.log(`✅ 磁力链接已存在，跳过: ${url}`)
    return true
  }

  console.log(`❌ 磁力链接不存在，继续解析: ${url}`)
  return false
}

/**
 * 初始化磁力链接状态
 */
function initializeMagnetLinkState(url: string): IOtherUrlDetailInfo {
  // 更新解析状态
  urlExtraDataMap.value[url] = {
    status: 'loading' as const,
    torrentPath: '',
  }

  // 创建初始的磁力链详情
  const initialMagnetInfo: IOtherUrlDetailInfo = {
    url,
    fileType: 'magnet',
    fileName: '',
    addTime: Date.now(),
    taskType: BaseType.TaskType.Magnet,
    detail: null,
    fileSize: 0,
    status: TaskParseStatus.LOADING,
  }

  dataMap.value[url] = initialMagnetInfo
  console.log('✅ 磁力链接状态初始化完成')
  return initialMagnetInfo
}

/**
 * 更新磁力链接错误状态
 */
function updateMagnetLinkError(url: string, errorMessage: string): void {
  urlExtraDataMap.value[url] = {
    status: 'error' as const,
    torrentPath: '',
  }

  const errorMagnetInfo: IOtherUrlDetailInfo = {
    url,
    fileType: 'magnet',
    fileName: errorMessage,
    addTime: Date.now(),
    taskType: BaseType.TaskType.Magnet,
    detail: null,
    fileSize: 0,
    status: TaskParseStatus.ERROR,
  }

  dataMap.value[url] = errorMagnetInfo
  console.warn('❌ 磁力链接错误:', errorMessage)
}

/**
 * 解析磁力链接并下载种子文件
 */
async function parseMagnetAndDownloadTorrent({ url, forceReparse = false }): Promise<boolean> {
  console.log('🚀 开始解析磁力链接:', url)

  // 检查是否已存在
  if (checkMagnetLinkExists(url, forceReparse)) {
    return true
  }

  try {
    // 初始化磁力链接状态
    const initialMagnetInfo = initializeMagnetLinkState(url)

    // 解析磁力链接
    console.log('🔧 开始解析磁力链接')
    const magnetParseResult = await DkHelper.parseMagnetUrl(url)

    if (!magnetParseResult || !magnetParseResult.infoHash) {
      console.error('❌ 解析磁力链接失败或缺少 infoHash')
      updateMagnetLinkError(url, '解析失败')
      return false
    }

    console.log(
      '✅ [parseMagnetAndDownloadTorrent] 磁力链接解析成功，infoHash:',
      magnetParseResult.infoHash
    )

    // 使用 infoHash 作为种子文件名
    const torrentTaskName = `${magnetParseResult.infoHash}.torrent`
    console.log('📁 [parseMagnetAndDownloadTorrent] 种子文件名:', torrentTaskName)

    console.log('🔧 [parseMagnetAndDownloadTorrent] 第四步：获取任务管理器单例')
    // 获取任务管理器单例
    const taskManager = TaskManager.GetInstance()
    console.log('✅ [parseMagnetAndDownloadTorrent] 任务管理器获取成功')

    console.log('🔧 [parseMagnetAndDownloadTorrent] 第五步：获取torrent文件保存路径')
    // 获取torrent文件保存路径
    const torrentFilePath = await getTorrentFilePath(torrentTaskName)
    console.log('📁 [parseMagnetAndDownloadTorrent] torrent文件路径:', torrentFilePath)

    console.log('🔧 [parseMagnetAndDownloadTorrent] 第六步：创建磁力链接下载任务参数')
    // 创建磁力链接下载任务
    const magnetTaskParams = {
      taskInfo: {
        background: true,
        taskType: BaseType.TaskType.Magnet,
        taskBaseInfo: {
          savePath: taskSavePath.value,
          taskName: torrentTaskName,
        },
      },
      magnetInfo: {
        url,
        torrentFilePath: path.dirname(torrentFilePath), // 使用torrent文件所在的目录
      },
    }
    console.log('📋 [parseMagnetAndDownloadTorrent] 磁力任务参数:', magnetTaskParams)

    console.log('🔧 [parseMagnetAndDownloadTorrent] 第七步：创建磁力任务')
    const magnetTask = await taskManager.createTask(magnetTaskParams)

    if (magnetTask) {
      console.log(
        '✅ [parseMagnetAndDownloadTorrent] 磁力任务创建成功，任务ID:',
        magnetTask.getId()
      )

      console.log('📝 [parseMagnetAndDownloadTorrent] 第八步：设置torrentPath')
      // 确保存在条目并设置torrentPath
      if (urlExtraDataMap.value[url]) {
        urlExtraDataMap.value[url].torrentPath = torrentFilePath
        console.log('✅ [parseMagnetAndDownloadTorrent] torrentPath设置完成:', torrentFilePath)
      }

      console.log('🔧 [parseMagnetAndDownloadTorrent] 第九步：启动磁力任务')
      // 启动任务
      await magnetTask.start()
      console.log('✅ [parseMagnetAndDownloadTorrent] 磁力任务启动成功')

      console.log('🔧 [parseMagnetAndDownloadTorrent] 第十步：监听任务状态变化')
      // 监听任务状态变化
      return new Promise((resolve, reject) => {
        console.log('📡 [parseMagnetAndDownloadTorrent] 开始监听任务状态变化')

        const statusChangeHandler = async (
          task: Task,
          oldStatus: BaseType.TaskStatus,
          newStatus: BaseType.TaskStatus
        ) => {
          console.log(
            `🔄 [parseMagnetAndDownloadTorrent] 任务状态变化 - 任务ID: ${task.getId()}, 旧状态: ${oldStatus}, 新状态: ${newStatus}`
          )

          if (task.getId() === magnetTask.getId()) {
            console.log('✅ [parseMagnetAndDownloadTorrent] 状态变化匹配当前磁力任务')

            if (newStatus === BaseType.TaskStatus.Succeeded) {
              console.log('🎉 [parseMagnetAndDownloadTorrent] 磁力任务下载成功')
              try {
                console.log('🔧 [parseMagnetAndDownloadTorrent] 开始解析BT种子文件')
                // 解析bt种子
                const info = await DkHelper.parseBtTaskInfo(urlExtraDataMap.value[url]?.torrentPath)
                console.log('📊 [parseMagnetAndDownloadTorrent] BT种子解析结果:', info)

                if (info) {
                  console.log('✅ [parseMagnetAndDownloadTorrent] BT种子解析成功')

                  console.log('📝 [parseMagnetAndDownloadTorrent] 更新 urlStatusMap 状态为成功')
                  // 第三步：解析成功，更新 urlStatusMap 状态为成功
                  if (urlExtraDataMap.value[url]) {
                    urlExtraDataMap.value[url].status = 'success'
                  }

                  console.log('📝 [parseMagnetAndDownloadTorrent] 更新 dataMap 中的磁力链详情')
                  // 第四步：更新 dataMap 中的磁力链详情
                  const successMagnetInfo: IOtherUrlDetailInfo = {
                    ...initialMagnetInfo,
                    fileName: '',
                    fileSize:
                      info.fileLists?.reduce((sum, file) => sum + (file.fileSize || 0), 0) || 0,
                    status: TaskParseStatus.SUCCESS,
                    detail: { ...info },
                    ...info,
                  }

                  dataMap.value[url] = successMagnetInfo

                  console.log('✅ [parseMagnetAndDownloadTorrent] 磁力链详情更新完成')
                  console.log(
                    `${url} 磁力链详情:`,
                    dataMap.value[url],
                    '所有文件详情',
                    dataMap.value
                  )

                  console.log('🔧 [parseMagnetAndDownloadTorrent] 移除事件监听器')
                  // 移除事件监听器
                  taskManager.detachTaskStatusChangeEvent(statusChangeHandler)

                  console.log('🚪 [parseMagnetAndDownloadTorrent] 函数执行结束 - 成功')
                  resolve(true)
                } else {
                  console.error('❌ [parseMagnetAndDownloadTorrent] BT种子解析失败')
                  // 解析失败，更新状态
                  const failedMagnetInfo: IOtherUrlDetailInfo = {
                    ...initialMagnetInfo,
                    status: TaskParseStatus.ERROR,
                  }
                  dataMap.value[url] = failedMagnetInfo
                  if (urlExtraDataMap.value[url]) {
                    urlExtraDataMap.value[url].status = 'error'
                  }

                  console.warn('❌ [parseMagnetAndDownloadTorrent] 磁力链解析失败')
                  taskManager.detachTaskStatusChangeEvent(statusChangeHandler)
                  reject(new Error('解析种子文件失败'))
                }
              } catch (error) {
                console.error('❌ [parseMagnetAndDownloadTorrent] 解析种子文件时出错:', error)
                // 解析出错，更新状态
                const errorMagnetInfo: IOtherUrlDetailInfo = {
                  ...initialMagnetInfo,
                  status: TaskParseStatus.ERROR,
                }
                dataMap.value[url] = errorMagnetInfo
                if (urlExtraDataMap.value[url]) {
                  urlExtraDataMap.value[url].status = 'error'
                }

                console.warn('❌ [parseMagnetAndDownloadTorrent] 磁力链解析出错')
                taskManager.detachTaskStatusChangeEvent(statusChangeHandler)
                reject(error)
              }
            } else if (newStatus === BaseType.TaskStatus.Failed) {
              console.warn('❌ [parseMagnetAndDownloadTorrent] 磁力任务失败')
              // 任务失败，更新状态
              const failedMagnetInfo: IOtherUrlDetailInfo = {
                ...initialMagnetInfo,
                fileName: '磁力任务下载失败',
                status: TaskParseStatus.ERROR,
              }
              dataMap.value[url] = failedMagnetInfo
              if (urlExtraDataMap.value[url]) {
                urlExtraDataMap.value[url].status = 'error'
              }

              console.warn('❌ [parseMagnetAndDownloadTorrent] 磁力任务下载失败')
              taskManager.detachTaskStatusChangeEvent(statusChangeHandler)
              reject(new Error('磁力任务下载失败'))
            } else {
              console.log(`📊 [parseMagnetAndDownloadTorrent] 任务状态: ${newStatus} - 继续等待`)
            }
          } else {
            console.log(`📊 [parseMagnetAndDownloadTorrent] 状态变化不匹配当前任务，跳过`)
          }
        }

        console.log('📡 [parseMagnetAndDownloadTorrent] 注册任务状态变化监听器')
        taskManager.attachTaskStatusChangeEvent(statusChangeHandler)
      })
    } else {
      console.log('❌ [parseMagnetAndDownloadTorrent] 磁力任务创建失败')
      console.log('磁力-1 ： 解析磁力链 任务创建失败:', url, 'taskSavePath', taskSavePath.value)

      console.log('📝 [parseMagnetAndDownloadTorrent] 更新失败状态')
      // 任务创建失败，更新状态
      const failedMagnetInfo: IOtherUrlDetailInfo = {
        ...initialMagnetInfo,
        status: TaskParseStatus.ERROR,
      }
      dataMap.value[url] = failedMagnetInfo
      if (urlExtraDataMap.value[url]) {
        urlExtraDataMap.value[url].status = 'error'
      }

      console.warn('❌ [parseMagnetAndDownloadTorrent] 磁力任务创建失败')
      console.log('🚪 [parseMagnetAndDownloadTorrent] 函数执行结束 - 任务创建失败')
      return false
    }
  } catch (error) {
    console.error('❌ [parseMagnetAndDownloadTorrent] 函数执行过程中发生异常:', error)
    // 更新 dataMap 状态为错误
    const errorMagnetInfo: IOtherUrlDetailInfo = {
      url,
      fileType: 'magnet',
      addTime: Date.now(),
      taskType: BaseType.TaskType.Magnet,
      detail: null,
      fileSize: 0,
      status: TaskParseStatus.ERROR,
    }
    dataMap.value[url] = errorMagnetInfo

    console.warn('❌ [parseMagnetAndDownloadTorrent] 创建磁力链接下载任务失败:', error)
    console.log('🚪 [parseMagnetAndDownloadTorrent] 函数执行结束 - 异常退出')
    return false
  }
}

/**
 * 第二步：从种子文件创建BT下载任务
 * Create BT download task from torrent file
 *
 * @param torrentPath 种子文件路径 Path to the torrent file
 * @param originalMagnetUrl 原始磁力链接 Original magnet URL
 * @param selectedFileIndices 要下载的文件索引数组，默认全选 Array of indices of files to download, default all
 * @returns Promise<Task | undefined> 创建的任务对象
 */
async function createBtTaskFromTorrent(
  torrentPath: string,
  originalMagnetUrl: string,
  selectedFileIndices?: number[]
): Promise<Task | undefined> {
  try {
    // 使用已解析的磁力任务信息（数组中的第一个）
    const dataInfo = dataMap.value[originalMagnetUrl]
    if (!dataInfo) {
      console.error('没有可用的种子信息')
      return undefined
    }

    // 如果没有指定要下载的文件，默认下载所有文件
    const fileIndices = selectedFileIndices || dataInfo.fileLists.map((_: any, index: number) => index)

    // bt任务参数
    const btTaskParams = {
      taskInfo: {
        background: false,
        taskType: BaseType.TaskType.Bt,
        taskBaseInfo: {
          savePath: taskSavePath.value,
          taskName: getTaskDisplayName(originalMagnetUrl, dataInfo),
        },
      },
      btInfo: {
        origin: originalMagnetUrl,
        seedFile: torrentPath,
        displayName:
          getTaskDisplayName(originalMagnetUrl, dataInfo) ||
          generateDefaultMagnetTaskName(originalMagnetUrl),
        fileRealIndexLists: fileIndices,
        fileLists: dataInfo.fileLists || [],
        infoId: dataInfo.infoId || '',
        tracker: dataInfo.trackerUrls?.join(',') || '',
        subFileScheduler: createTaskStore.getSubFileSchedulerType(),
      },
    }

    console.log('创建磁力链任务参数', btTaskParams, 'dataInfo', dataInfo)
    // 创建BT下载任务
    const result = await TaskManager.GetInstance().checkRepeatAndCreateTask(btTaskParams)
    const { task: btTask, exist } = result

    if (exist) {
      console.info(
        '⚠️ BT任务已存在，跳过创建: btTask',
        btTask,
        getTaskDisplayName(originalMagnetUrl, dataInfo)
      )
      // 新增：将重复任务信息添加到列表中
      if (btTask) {
        addDuplicateTaskId(btTask.getId(), BaseType.TaskType.Bt, originalMagnetUrl)
        throw createTaskError('existed', 'BT任务已存在', {
          taskType: BaseType.TaskType.Bt,
          taskId: btTask.getId(),
          url: originalMagnetUrl,
        })
      }
      return
    } else {
      console.info('✅ BT任务创建成功:', btTask?.getId(), btTask, 'btTaskParams', btTaskParams)
    }

    if (btTask) {
      // 启动任务
      await btTask.start()
      console.log('toast BT任务启动成功:')
      // 调用封装的成功提示函数
      showMessage('创建任务成功', 'success', 'task_confirmed')

      return btTask
    }

    return undefined
  } catch (error) {
    console.error('从种子文件创建BT任务失败:', error)
    return undefined
  }
}

/**
 * 创建eMule下载任务（电驴下载）
 * @param emuleUrlOrTaskInfo 电驴链接或Emule任务信息对象 eMule link or Emule task info object
 */
async function createEmuleTask(emuleUrlOrTaskInfo: string | EmuleTaskInfo) {
  console.log('创建eMule任务:', emuleUrlOrTaskInfo)

  // 标记当前正在创建的任务信息，用于更新状态
  let taskInfoForUpdate: EmuleTaskInfo | undefined

  try {
    let emuleUrl: string
    let fileName: string

    if (typeof emuleUrlOrTaskInfo === 'string') {
      emuleUrl = emuleUrlOrTaskInfo
      // 电驴链接格式通常为: ed2k://|file|文件名|文件大小|文件哈希|/
      const parts = emuleUrl.split('|')
      if (parts.length >= 4) {
        fileName = decodeURIComponent(parts[2])
      } else {
        fileName = `Emule下载_${Date.now()}`
      }
    } else {
      // 如果是Emule任务信息对象，使用其中的信息
      emuleUrl = emuleUrlOrTaskInfo.url
      fileName = emuleUrlOrTaskInfo.fileName
      taskInfoForUpdate = emuleUrlOrTaskInfo
    }

    // 如果是从Emule任务信息创建的，更新任务状态为创建中
    if (taskInfoForUpdate) {
      const currentTaskInfo = taskInfoForUpdate as EmuleTaskInfo
      const index = emuleTaskArray.value.findIndex(item => item.infoId === currentTaskInfo.infoId)
      if (index > -1) {
        const updatedTask = {
          ...currentTaskInfo,
          status: EmuleTaskStatus.CREATING,
        }
        emuleTaskArray.value = [
          ...emuleTaskArray.value.slice(0, index),
          updatedTask,
          ...emuleTaskArray.value.slice(index + 1),
        ]
      }
    }

    // 创建eMule任务
    const result = await TaskManager.GetInstance().checkRepeatAndCreateTask({
      taskInfo: {
        background: false,
        taskType: BaseType.TaskType.Emule,
        taskBaseInfo: {
          savePath: taskSavePath.value,
          taskName: fileName,
        },
      },
      emuleInfo: {
        url: emuleUrl,
      },
    })

    const { task: emuleTask, exist } = result

    if (exist) {
      console.log('⚠️ eMule任务已存在，跳过创建:', fileName)
      // 新增：将重复任务信息添加到列表中
      if (emuleTask) {
        addDuplicateTaskId(emuleTask.getId(), BaseType.TaskType.Emule, emuleUrl)
        throw createTaskError('existed', 'Emule任务已存在', {
          taskType: BaseType.TaskType.Emule,
          taskId: emuleTask.getId(),
          url: emuleUrl,
        })
      }
      return
    } else {
      console.log('✅ eMule任务创建成功:', emuleTask?.getId())
    }

    if (emuleTask) {
      await emuleTask.start()
      console.log('eMule任务启动成功:', emuleTask.getId())

      // 关闭新建任务对话框
      closeWindow('emule_task_created')
    } else {
      console.error('eMule任务创建失败')
    }
  } catch (error) {
    console.error('创建eMule任务失败:', error)
  }
}

async function createGroupTask(options: {
  savePath: string
  taskName: string
  background: boolean
  subTasks: BaseType.NewGroupTaskInfo[]
}) {
  const { savePath, taskName, background, subTasks } = options

  console.log('创建任务组，包含任务数量:', options, 'taskSavePath.value', taskSavePath.value)

  try {
    // 将任务组名称拼接到保存路径后，为任务组创建专门的文件夹
    const groupSavePath = savePath
    const savePathWithGroupName = path.join(savePath, taskName)

    // 遍历每个子任务，将它们的保存路径统一设置为任务组文件夹
    const updatedSubTasks = subTasks.map(subTask => {
      return {
        ...subTask,
        baseInfo: {
          ...subTask.baseInfo,
          taskBaseInfo: {
            ...subTask.baseInfo.taskBaseInfo,
            savePath: savePathWithGroupName,
          },
        },
      }
    })

    // 使用 BaseType.NewTaskSet 的正确结构创建任务组
    const createGroupTaskParams: BaseType.NewTaskSet = {
      taskInfo: {
        background: background,
        taskType: BaseType.TaskType.Group,
        taskBaseInfo: {
          savePath: groupSavePath,
          taskName: taskName,
        },
      },
      groupInfo: updatedSubTasks, // 使用更新后的子任务信息
    }

    const groupTask = await TaskManager.GetInstance().createTask(createGroupTaskParams)

    console.info('任务组创建成功✅:', groupTask, '任务组参数', createGroupTaskParams)

    if (groupTask) {
      // 启动任务
      await groupTask.start()

      return groupTask
    }
    return undefined
  } catch (error) {
    console.error('创建任务组失败:', error)
    return undefined
  }
}

const handleCancel = async () => {
  console.log('Task creation cancelled')
  isLoading.value = true
  await new Promise(resolve => setTimeout(resolve, 3000))
  isLoading.value = false
}





async function savePendingAllTasks() {
  console.log('[CreateTask] 使用新的LinkSaver工具保存所有待处理任务')

  try {
    const linkSaver = LinkSaver.getInstance()

    const options: LinkSaveOptions = {
      scene: LinkSaveScene.CREATE_TASK,
      actions: ['ACTION_DOWNLOAD'], // 创建任务场景下使用下载操作
      ignoreEvent: false,
    }

    const result = await linkSaver.savePendingAllTasks({
      allUrlsWithType: allUrlsWithType.value,
      dataMap: dataMap.value,
      urlExtraDataMap: urlExtraDataMap.value,
      checkedFileIndexes: checkedFileIndexes.value,
      optionsExtData: optionsExtData.value, // 添加 optionsExtData 参数
      options,
    })

    console.log('[CreateTask] 链接保存结果:', result)

    if (result.success) {
      console.log(`[CreateTask] 成功保存 ${result.savedCount} 个任务到链接中心`)
    } else {
      console.warn(`[CreateTask] 保存完成，成功: ${result.savedCount}，失败: ${result.failedCount}`)
      result.errors.forEach(error => {
        console.error(`[CreateTask] 保存失败: ${error.url} - ${error.error}`)
      })
    }

    return result
  } catch (error) {
    console.error('[CreateTask] 保存所有待处理任务时出错:', error)
    throw error
  }
}

/**
 * 解析Emule链接并添加到任务数组
 * Parse Emule link and add to task array
 *
 * @param params 包含url的参数对象
 * @returns Promise<IEmuleUrlDetailInfo | null> 成功时返回Emule任务详细信息对象，失败时返回null
 */
async function getEmuleTaskInfo(params: { url: string }): Promise<IEmuleUrlDetailInfo | null> {
  const url = params.url

  try {
    console.log(`开始解析Emule URL:`, url)

    // 直接调用DkHelper.parserEd2kLink解析Emule链接
    const parseResult = await DkHelper.parserEd2kLink(url)

    if (parseResult && parseResult.fileName && parseResult.fileHash) {
      const fileHash = parseResult.fileHash

      // 使用 fileHash 作为 key 检查是否已存在相同的文件
      const exists = dataMap.value[fileHash]
      if (exists) {
        console.log(`Emule文件已存在（fileHash: ${fileHash}），跳过:`, url)
        return exists as IEmuleUrlDetailInfo
      }

      // 解析成功，创建并存储 Emule 任务信息，符合 IEmuleUrlDetailInfo 接口
      const fileType = detectFileType(url)
      const successEmuleInfo: IEmuleUrlDetailInfo = {
        // IUrlDetailInfoBase 基础字段
        url,
        fileType,
        fileName: parseResult.fileName,
        addTime: Date.now(),
        taskType: BaseType.TaskType.Emule,
        detail: parseResult,
        fileSize: parseResult.fileSize || 0,

        // Emule 特有字段
        fileHash: parseResult.fileHash,
      }

      // 使用 fileHash 作为 key 存储到 dataMap
      dataMap.value[url] = successEmuleInfo

      console.log(
        `${url} Emule URL 详情（fileHash: ${fileHash}）:`,
        dataMap.value[fileHash],
        '所有文件详情',
        dataMap.value
      )
      return successEmuleInfo
    } else {
      console.log('Emule链接解析失败，返回结果为空或无效:', url, parseResult)
      return null
    }
  } catch (error) {
    console.error('解析Emule链接时出错:', error, 'URL:', url)
    return null
  }
}

const handleCheckedFileIndexes = (data: TaskFileSelectionMap) => {
  // 直接存储data
  checkedFileIndexes.value = data
  console.log('磁力-5 ： 选中文件下标 原始数据:', data)
}

function getTaskSettings(taskId: string): TaskSettings {
  return taskSettingStore.getTaskSettings(taskId)
}

/**
 * 创建P2SP下载任务（带设置）
 * @param url 下载链接 Download URL
 * @param settings 任务设置 Task settings
 * @returns 创建的任务对象 The created task object
 */
async function createP2spTask(url: string, settings: TaskSettings) {
  console.info('即将创建P2SP下载任务（带设置）', url, '地址', taskSavePath.value, '设置', settings)

  // try {
  // 使用统一的函数获取最终任务名称，优先使用自定义名称
  const taskName = getTaskDisplayName(url)

  // 基础的 P2SP 任务信息
  let p2spInfo: any = {
    url: url,
    refUrl: settings.downloadUrl || url,
    useOriginResourceOnly: settings.downloadFromOriginalOnly,
    originResourceThreadCount: settings.threadCount,
    loginFtp: settings.loginFtpServer,
    ftpUserName: settings.ftpUsername,
    ftpPassword: settings.ftpPassword,
    openAfterDownload: settings.openAfterDownload,
    origin: '',
  }

  // 如果是FTP链接，解析FTP URL的详细信息并应用到任务参数中
  if (isFtpUrl(url)) {
    try {
      console.log('检测到FTP链接，开始解析FTP详细信息:', url)
      const ftpParseResult = await DkHelper.parseP2spUrl(url)

      if (ftpParseResult) {
        console.log('FTP解析成功，应用FTP详细信息:', ftpParseResult)

        // 如果解析结果中包含用户名和密码，优先使用解析结果
        if (ftpParseResult.userName && ftpParseResult.password) {
          p2spInfo.loginFtp = true
          p2spInfo.ftpUserName = ftpParseResult.userName
          p2spInfo.ftpPassword = ftpParseResult.password
          console.log('使用FTP URL中的认证信息:', {
            userName: ftpParseResult.userName,
            password: '***', // 不记录明文密码
          })
        }
        // 如果设置中有FTP认证信息，使用设置中的信息
        else if (settings.loginFtpServer && settings.ftpUsername) {
          p2spInfo.loginFtp = true
          p2spInfo.ftpUserName = settings.ftpUsername
          p2spInfo.ftpPassword = settings.ftpPassword
          console.log('使用任务设置中的FTP认证信息')
        }
      } else {
        console.log('FTP URL解析失败，使用默认设置')
      }
    } catch (error) {
      console.error('解析FTP URL时出错:', error)
      // 如果解析失败，仍然使用设置中的FTP信息
      if (settings.loginFtpServer) {
        p2spInfo.loginFtp = true
        p2spInfo.ftpUserName = settings.ftpUsername
        p2spInfo.ftpPassword = settings.ftpPassword
      }
    }
  }

  const taskParams = {
    taskInfo: {
      background: false,
      taskType: BaseType.TaskType.P2sp,
      taskBaseInfo: {
        savePath: taskSavePath.value,
        taskName: taskName,
        openOnComplete: settings.openAfterDownload ? 1 : 0,
      },
    },
    p2spInfo: p2spInfo,
  }

  // 使用 checkRepeatAndCreateTask 替代 createTask
  const result = await TaskManager.GetInstance().checkRepeatAndCreateTask(taskParams)
  const { task: p2spTask, exist } = result

  if (exist) {
    console.info('⚠️ P2SP任务已存在，跳过创建:', url)
    // 新增：将重复任务信息添加到列表中
    if (p2spTask) {
      addDuplicateTaskId(p2spTask.getId(), BaseType.TaskType.P2sp, url)
      throw createTaskError('existed', 'P2SP任务已存在', {
        taskType: BaseType.TaskType.P2sp,
        taskId: p2spTask.getId(),
        url: url,
      })
    }
    return
  } else {
    console.info('✅P2SP 下载任务创建成功（带设置）:', p2spTask, '参数:', taskParams)
  }

  if (p2spTask) {
    await p2spTask.start()
    return p2spTask
  }
  return undefined
}

/**
 * 记录下载路径到历史记录
 * 纯函数：接收路径参数，返回记录结果，无外部依赖
 * Record download path to history
 * Pure function: receives path parameter, returns recording result, no external dependencies
 *
 * @param savePath 要记录的下载路径 The download path to record
 * @param type 下载路径类型 The type of download path
 * @returns Promise<boolean> 返回是否成功记录 Returns whether the recording was successful
 */
async function recordDownloadPath(savePath: string, type: DownloadPathType): Promise<boolean> {
  // 参数验证
  if (!savePath || !savePath.trim()) {
    console.warn('记录下载路径失败：路径为空')
    return false
  }

  try {
    if (type === DownloadPathType.Local) {
      await DownloadPathNS.addPath(savePath.trim())
    } else if (type === DownloadPathType.Cloud) {
      await downloadCloudPathStore.setRecentSaveDefaultFolder(
        downloadCloudPathStore.getCurrentCloudPath()!
      )
    }

    // 记录上次使用的类型
    config.setValue('TaskDefaultSettings', 'LogicChoosed', type === DownloadPathType.Local)
    config.setValue('TaskDefaultSettings', 'CloudChoosed', type === DownloadPathType.Cloud)!
    console.log('成功记录下载路径到历史记录:', savePath)
    return true
  } catch (error) {
    console.error('记录下载路径失败:', error, '路径:', savePath)
    return false
  }
}

async function handleDownloadButtonSubmit(params: {
  type: DownloadPathType
  path?: string
  scene?: string
}) {
  const { type, path = '', scene } = params

  console.log('[CreateTask] 收到 DownloadButton:', type, '路径:', path, '场景:', scene)

  if (type === DownloadPathType.Local) {
    handleDownload()
  } else if (type === DownloadPathType.Cloud) {
    // 调用添加到云盘逻辑，传递云盘父文件夹ID, path是空字符串，表示是添加到云盘根目录
    if (freeCloudAddCount.value <= 0) {
      XMPMessage({
        message: '今日云添加次数已用完，请明日再试',
        type: 'warning',
      })
    } else {
      handleCloudDownload(path)
    }
  }
}

/**
 * 异步保存云盘下载链接到 LinkHub
 * @param parentId 云盘父文件夹ID
 */
async function saveCloudDownloadLinksToHub(parentId: string) {
  try {
    const linkSaver = LinkSaver.getInstance()

    const options: LinkSaveOptions = {
      scene: LinkSaveScene.BATCH_OPERATION,
      actions: ['ACTION_CLOUD_DOWNLOAD'],
      ignoreEvent: false,
      customData: {
        parentId: parentId,
        timestamp: Date.now(),
      },
    }

    await linkSaver.savePendingAllTasks({
      allUrlsWithType: allUrlsWithType.value,
      dataMap: dataMap.value,
      urlExtraDataMap: urlExtraDataMap.value,
      checkedFileIndexes: checkedFileIndexes.value,
      optionsExtData: optionsExtData.value, // 添加 optionsExtData 参数
      options,
    })

    console.log('[CreateTask] 云盘下载链接已保存到 LinkHub')
  } catch (linkSaveError) {
    console.error('[CreateTask] 保存云盘下载链接失败:', linkSaveError)
    // 链接保存失败不影响云盘下载流程
  }
}

/**
 * 处理云盘下载
 * @param parentId 云盘父文件夹ID
 */
async function handleCloudDownload(parentId: string) {
  console.log('开始云盘下载，父文件夹ID:', parentId)

  try {
    // 首先检查云盘空间是否足够
    const hasEnoughSpace = await checkCloudDriveSpace()
    if (!hasEnoughSpace) {
      console.log('[CreateTask] 云盘空间不足，取消云盘下载操作')
      return
    }

    // 调用批量添加到云盘接口
    await batchAddUrlsToDrive(parentId)

    // 异步保存链接到 LinkHub，不阻塞主流程
    saveCloudDownloadLinksToHub(parentId)

    console.log('toast 任务成功1')

    // 记录下载路径
    await recordDownloadPath(downloadCloudPathStore.currentPathDisplayName, DownloadPathType.Cloud)

    // closeWindow('cloud_download_completed')
    // 调用封装的成功提示函数
    showMessage('已加入云添加列表', 'success', 'cloud_download_completed')
  } catch (error) {
    console.error('云盘下载过程中出错:', error)
  }
}

/**
 * 批量添加任务到云盘
 * @param parentId 云盘父文件夹ID
 * @param groupName 可选的任务组名称
 */
async function batchAddUrlsToDrive(parentId: string, groupName?: string) {
  console.log('开始批量添加任务到云盘，父文件夹ID:', parentId, '任务组名称:', groupName)

  try {
    // 获取当前云盘路径
    const currentCloudPath = downloadCloudPathStore.getCurrentCloudPath()
    console.log('[CreateTask] 当前云盘路径:', currentCloudPath)

    const tasks: any[] = []

    // 遍历所有URL，根据taskType处理不同类型的任务
    for (const urlWithType of allUrlsWithType.value) {
      const { url, taskType } = urlWithType
      const dataInfo = dataMap.value[url]

      if (!dataInfo) {
        console.warn('找不到任务数据信息:', url)
        continue
      }

      // 根据任务类型处理
      switch (taskType) {
        case BaseType.TaskType.Magnet:
          // 处理磁力链任务
          const magnetStatus = urlExtraDataMap.value[url]?.status
          if (magnetStatus === 'success') {
            const isSelected = checkedFileIndexes.value[url]?.fileIndexes?.length > 0

            if (isSelected) {
              // 获取选中的文件下标
              const selectedFileIndexes = getCheckedFileIndexedByUrl({ url })

              // 构造磁力链任务数据
              const magnetTask = {
                path: currentCloudPath?.fullFolderpathStr || '',
                name: dataInfo.title || dataInfo.fileName || generateDefaultMagnetTaskName(url),
                url: url,
                parentId: parentId,
                files: selectedFileIndexes.map(index => index.toString()), // 将文件下标转换为字符串数组
                play: false,
                space: '', // 根据需要设置空间类型
              }
              tasks.push(magnetTask)
              console.log('添加磁力链任务到批量任务列表:', magnetTask)
            }
          }
          break

        case BaseType.TaskType.P2sp:
          // 处理P2SP任务
          const isSelected = checkedFileIndexes.value[url]?.fileIndexes?.length > 0

          if (isSelected) {
            const p2spTask = {
              path: currentCloudPath?.fullFolderpathStr || '',
              name:
                dataInfo.fileName ||
                dataInfo.title ||
                extractFileNameFromUrlUtil(url) ||
                'unknown-file',
              url: url,
              parentId: parentId,
              files: [], // P2SP任务没有子文件，设置为空数组
              play: false,
              space: '',
            }
            tasks.push(p2spTask)
            console.log('添加P2SP任务到批量任务列表:', p2spTask)
          }
          break

        case BaseType.TaskType.Emule:
          // 处理Emule任务
          const emuleIsSelected = checkedFileIndexes.value[url]?.fileIndexes?.length > 0

          if (emuleIsSelected && dataInfo.detail) {
            const emuleTask = {
              path: currentCloudPath?.fullFolderpathStr || '',
              name: dataInfo.fileName || dataInfo.title || 'Emule任务',
              url: url,
              parentId: parentId,
              files: [], // Emule任务没有子文件，设置为空数组
              play: false,
              space: '',
            }
            tasks.push(emuleTask)
            console.log('添加Emule任务到批量任务列表:', emuleTask)
          }
          break

        default:
          console.warn('未知的任务类型:', taskType, url)
          break
      }
    }

    if (tasks.length > 0) {
      // 调用批量添加到云盘接口
      const options: any = {
        autoSelectTab: true,
      }
      if (groupName) {
        options.groupName = groupName
      }

      console.log(
        '调用批量添加到云盘接口，任务数量:',
        tasks.length,
        '参数:',
        tasks,
        '选项:',
        options
      )

      const result = await ThunderPanClientSDK.getInstance().batchAddUrlsToDrive(tasks, options)

      console.log('批量添加到云盘结果:', result)

      if (result.success) {
        console.log('批量添加到云盘成功:', result.data)

        // 可以在这里添加成功提示
        // XMPMessage({
        //   message: `成功添加 ${tasks.length} 个任务到云盘`,
        //   type: 'success',
        // })
      } else {
        console.error('批量添加到云盘失败:', result.error)
        throw new Error(result.error || '批量添加到云盘失败')
      }
    } else {
      console.log('没有选中的任务需要添加到云盘')
    }
  } catch (error) {
    console.error('批量添加到云盘时出错:', error)
    throw error
  }
}

//自动拉起的任务-下载
function handleAutoCreateDownload(params: DownloadEventParams) {
  console.log('[create] handleAutoCreateDownload:', params)
  checkedFileIndexes.value = params.checkedFileIndexes
  // 将 DownloadEventParams 转换为 handleDownloadButtonSubmit 的参数格式
  const downloadPathType =
    params.type === 'download' ? DownloadPathType.Local : DownloadPathType.Cloud
  handleDownloadButtonSubmit({
    type: downloadPathType,
    path: params.path,
    scene: 'auto-create-task',
  })
}

//接收参数， 关闭自动创建任务 (保持原有签名兼容性)
function handleAutoCreateTaskClose(params: { scene: string }) {
  console.log('handleAutoCreateTaskClose 被调用，参数:', params)

  if (!params || !params.scene || !params.scene.trim()) {
    console.error('❌ 关闭窗口失败：场景编号为空')
    return
  }

  closeWindow(params.scene.trim())
}

/**
 * 处理 TaskLaterButton 成功事件
 */
const handleLaterSuccess = (result: {
  success: boolean
  message: string
  savedCount?: number
  failedCount?: number
}) => {
  console.log('[CreateTask] TaskLaterButton 成功:', result)
  // TODO：加埋点
  // tasklaterbutton组件内部已弹出提示 关闭弹窗 跳转到全链接
  // 显示成功提示
  // XMPMessage({
  //   message: result.message,
  //   type: 'success',
  // })

  // showMessage(`已加入全部链接`, 'success', 'task_later_success')

  // toAllLinkPage()
  // 关闭面板
  // closeWindow('task_later_success')
}



/**
 * 处理 TaskLaterButton 错误事件
 */
const handleLaterError = (error: string) => {
  console.error('[CreateTask] TaskLaterButton 失败:', error)

  // 显示错误提示
  XMPMessage({
    message: error,
    type: 'error',
  })
}

// 新增：处理重复任务对话框事件
const handleDuplicateTaskClose = () => {
  console.log('用户关闭重复任务对话框')
  showDuplicateTaskDialog.value = false
  duplicateTaskDetails.value = []
  clearDuplicateTaskUrls()
}

const handleDuplicateTaskSkip = () => {
  console.log('用户选择跳过重复任务')

  // 重置所有重复任务相关的变量
  showDuplicateTaskDialog.value = false
  duplicateTaskDetails.value = []
  clearDuplicateTaskUrls()

  // 显示提示信息
  // XMPMessage({
  //   message: '已跳过重复任务',
  //   type: 'info',
  // })
}

const handleDuplicateTaskRedownload = async () => {
  console.log('用户选择重新下载重复任务')

  try {
    // 关闭对话框
    showDuplicateTaskDialog.value = false

    // 遍历所有重复任务ID，调用reDownload方法
    const redownloadPromises = duplicateTaskIds.value.map(async duplicateItem => {
      const { taskId, taskType, url } = duplicateItem

      try {
        console.log(`开始重新下载任务: taskId=${taskId}, taskType=${taskType}, url=${url}`)

        // 通过taskId获取任务对象
        const existingTask = await TaskManager.GetInstance().findTaskById(taskId)

        if (existingTask) {
          // 调用reDownload方法重新下载
          await existingTask.reDownload()
          console.log(`✅ 重新下载任务成功: taskId=${taskId}`)
          return { success: true, taskId, url }
        } else {
          console.warn(`❌ 找不到任务对象: taskId=${taskId}`)
          return { success: false, taskId, url, error: '任务不存在' }
        }
      } catch (err) {
        console.error(`❌ 重新下载任务失败: taskId=${taskId}`, err)
        return {
          success: false,
          taskId,
          url,
          error: err instanceof Error ? err.message : '重新下载失败',
        }
      }
    })

    // 等待所有重新下载操作完成
    const results = await Promise.allSettled(redownloadPromises)

    // 统计结果
    let successCount = 0
    let failureCount = 0

    results.forEach(result => {
      if (result.status === 'fulfilled') {
        const redownloadResult = result.value
        if (redownloadResult.success) {
          successCount++
        } else {
          failureCount++
        }
      } else {
        failureCount++
        console.error(`重新下载任务异常:`, result.reason)
      }
    })

    console.log(`重新下载完成 - 成功: ${successCount}, 失败: ${failureCount}`)

    // 显示结果提示
    if (successCount > 0 && failureCount === 0) {
      // showMessage(`成功重新下载 ${successCount} 个任务`, 'success', 'redownload_success')
      // 全部成功， 直接关闭新建任务弹窗
      closeWindow('redownload_success')
    } else if (successCount > 0 && failureCount > 0) {
      showMessage(
        `${successCount} 个任务重新下载成功，${failureCount} 个任务重新下载失败`,
        'warning'
      )
    } else {
      showMessage('重新下载任务失败', 'error')
    }
  } catch (err) {
    console.error('重新下载重复任务时出错:', err)
    showMessage(`重新下载失败: ${err instanceof Error ? err.message : '未知错误'}`, 'error')
  } finally {
    // 无论成功或失败，都要清理重复任务相关的状态
    duplicateTaskDetails.value = []
    clearDuplicateTaskUrls()
  }
}

async function showDuplicateTaskWindow() {
  const currentWindow = PopUpNS.getCurrentWindow()
  // 确定弹窗位置相关配置
  let popupOptions: any = {
    parentId: currentWindow.id || -1, // 传递-1，表示不以主窗口为父窗口
    relatePos: PopUpTypes.RelatePosType.CenterParent,
    replaceView: false, // 该值设置为true，则如果已经存在窗口，则更新数据到当前窗口
    singleton: false, // 关键：确保只有一个创建任务窗口
    title: '',
    windowWidth: 460,
    windowHeight: 278,
    duplicateTaskDetails: duplicateTaskDetails.value,
    optionsExtData: optionsExtData.value, // 添加 optionsExtData 参数
  }

  const payload = await PopUpNS.popup('duplicate-task', popupOptions, { alwaysOnTop: true })

  // 处理用户操作结果
  if (payload.action === PopUpTypes.Action.OK) {
    const args = payload.args as any
    const picked = args.picked

    console.log('用户选择的操作:', picked)

    if (picked === 'skip') {
      console.log('跳过')
      handleDuplicateTaskSkip()
    } else if (picked === 'redownload') {
      console.log('重新下载')
      handleDuplicateTaskRedownload()
    } else if (picked === 'cancel') {
      console.log('取消')
      handleDuplicateTaskClose()
    }
  } else {
    console.log('用户取消了重复任务提示')
  }
}

/**
 * 显示提示消息，支持配置消息类型和是否需要关闭窗口
 */
function showMessage(
  message: string,
  type: 'success' | 'error' | 'warning' = 'success',
  closeWindowScene?: string
) {
  XMPMessage({
    message,
    type,
    duration: 800,
    onClose: closeWindowScene ? () => closeWindow(closeWindowScene) : undefined,
  })
}

onMounted(async () => {
  const driveSpaceInfo = getLogicalDriveList()
  console.log('[CreateTask] 初始化时获取驱动器空间信息:', driveSpaceInfo)
  console.log('[CreateTask] 组件初始化时失焦状态:', isWindowBlurred.value)

  // 初始化认证状态
  await initializeAuth()

  // 检查登录状态，如果未登录则显示登录弹窗
  // const isLoggedIn = await requireAuth()
  // if (!isLoggedIn) {
  //   console.log('[CreateTask] 用户未登录，不弹出登录弹窗')
  //   // 标记已经在组件中显示了登录弹窗
  //   hasShownLoginDialog.value = true
  //   // 弹出登录弹窗
  //   showLoginDialog()
  //   // 注意：这里不需要等待，因为登录弹窗是异步的
  //   // 用户登录成功后，会通过事件监听器自动更新状态
  //   // return
  // }

  // 获取当前用户云添加次数
  createTaskStore.setCurrentUserCloudAddQuotas()

  console.log('[CreateTask] 用户已登录，继续初始化')
  // 继续组件的初始化逻辑
  // initializeComponentAfterLogin()

  // 新增：设置窗口失焦事件监听器
  console.log('[CreateTask] 设置窗口失焦事件监听器')

  // 监听窗口失焦事件
  useEventListener(window, 'blur', handleWindowBlur)

  // 监听窗口获得焦点事件
  useEventListener(window, 'focus', handleWindowFocus)

  // 监听文档可见性变化事件
  useEventListener(document, 'visibilitychange', handleVisibilityChange)

  // 监听页面隐藏事件（当用户切换到其他标签页或应用时）
  useEventListener(document, 'pagehide', handleWindowBlur)

  // 监听页面显示事件（当用户回到当前标签页时）
  useEventListener(document, 'pageshow', handleWindowFocus)
})

function setLoggedWindowSize() {
  overridePosition(defaultPositionOptions)
}
// 增加一个函数把窗口大小设置为 0 和 0
function setWindowSizeToZero() {
  const defaultPositionOptions = {
    autoSize: false,
    show: false,
    windowWidth: 0,
    windowHeight: 0,
    relatePos: PopUpTypes.RelatePosType.CenterParent,
  }

  overridePosition(defaultPositionOptions)
}
// 增加一个函数把窗口大小设置为 0 和 0
function setUnLoggedWindowSize() {
  setWindowSizeToZero()
}

// 监听登录状态变化，当用户登录成功时继续初始化
watch(
  () => userStore.isLogged,
  (newStatus, oldStatus) => {
    // 未登录-弹窗大小设置为0 * 0
    if (newStatus === false) {
      setUnLoggedWindowSize()
    }

    // 已登录-弹窗大小设置为680 * 316
    if (newStatus === true) {
      setLoggedWindowSize()
    }
    if (newStatus && !oldStatus) {
      // 登录状态从 false 变为 true，说明用户刚刚登录成功
      if (hasShownLoginDialog.value) {
        // 场景2：在组件中显示了登录弹窗后登录成功
        console.log('[CreateTask] 场景2：用户在组件中登录成功，继续初始化')
        hasShownLoginDialog.value = false // 重置标志
        initializeComponentAfterLogin()
      } else {
        // 场景1：打开组件时用户已经登录
        console.log('[CreateTask] 场景1：用户打开组件时已经登录')
        // 这种情况在 onMounted 中已经处理了，这里不需要重复处理
      }
    }
  }
)

// 新增：监听窗口失焦状态变化
watch(
  () => isWindowBlurred.value,
  isBlurred => {
    if (isBlurred) {
      // 窗口失焦时的处理逻辑
      // console.log('[CreateTask] 窗口失焦，执行相应处理')
      // 例如：暂停某些操作、保存状态等
      // 可以在这里添加具体的失焦处理逻辑
      // 例如：暂停动画、减少更新频率、隐藏敏感信息等
    } else {
      // 窗口获得焦点时的处理逻辑
      // console.log('[CreateTask] 窗口获得焦点，恢复相应处理')
      // 例如：恢复操作、刷新状态等
      // 可以在这里添加具体的焦点处理逻辑
      // 例如：恢复动画、增加更新频率、显示信息等
    }
  }
)

// 组件登录后的初始化逻辑
function initializeComponentAfterLogin() {
  console.log('[CreateTask] 开始组件登录后的初始化')
  // 在这里添加需要在用户登录后执行的初始化逻辑
  // 例如：加载用户数据、初始化任务列表等
}

// 添加 onUnmounted 清理事件监听器
onUnmounted(() => {
  // document.removeEventListener('click', handleClickOutside)
  // 组件销毁时重置所有 store 状态到初始值
  createTaskStore.resetAll()
  // 新增：清空重复任务列表
  clearDuplicateTaskUrls()
  // 清理认证相关资源
  cleanupAuth()

  // 新增：清理窗口失焦事件监听器
  console.log('[CreateTask] 清理窗口失焦事件监听器')
  // useEventListener 会自动清理事件监听器，所以这里不需要手动清理
  // 但我们可以在这里添加其他清理逻辑
})

/**
 * 检查云盘空间是否足够存储选中的文件
 * 通过遍历checkedFileIndexes获取选中文件的总大小，通过getCurrentUserDriveQuotas获取云盘剩余空间大小，
 * 判断文件总大小是否大于云盘的剩余空间，如果大于，直接提示空间不足
 * @returns Promise<boolean> 返回true表示空间足够，false表示空间不足
 */
async function checkCloudDriveSpace(): Promise<boolean> {
  try {
    console.log('[CreateTask] 开始检查云盘空间是否足够')

    // 使用新的函数获取选中文件的总大小
    const totalSelectedFileSize = getSelectedFilesTotalSize()

    // 获取云盘剩余空间
    const driveQuotasResult = await ThunderPanClientSDK.getInstance().getCurrentUserDriveQuotas()

    if (!driveQuotasResult.success || !driveQuotasResult.data) {
      console.error('[CreateTask] 获取云盘配额信息失败:', driveQuotasResult.error)
      showMessage('无法获取云盘空间信息，请稍后重试', 'warning')
      return false
    }

    const { surplus } = driveQuotasResult.data
    console.log(`[CreateTask] 云盘剩余空间: ${surplus} 字节`)

    // 比较文件总大小和云盘剩余空间
    if (totalSelectedFileSize > surplus) {
      console.warn(
        `[CreateTask] 云盘空间不足: 需要 ${totalSelectedFileSize} 字节，剩余 ${surplus} 字节`
      )

      showMessage(`云盘空间不足`, 'warning')

      return false
    }

    console.log('[CreateTask] 云盘空间充足，可以继续操作')
    return true
  } catch (error) {
    console.error('[CreateTask] 检查云盘空间时出错:', error)
    showMessage('检查云盘空间时出错，请稍后重试', 'error')
    return false
  }
}

/**
 * 获取选中文件的总大小
 * 通过遍历checkedFileIndexes计算所有选中文件的总大小
 * @returns number 返回选中文件的总大小（字节）
 */
function getSelectedFilesTotalSize(): number {
  let totalSelectedFileSize = 0

  // 遍历checkedFileIndexes，计算所有选中文件的总大小
  for (const [url, selectionInfo] of Object.entries(checkedFileIndexes.value)) {
    if (!selectionInfo?.fileIndexes?.length) {
      continue // 跳过没有选中文件的任务
    }

    const dataInfo = dataMap.value[url]
    if (!dataInfo) {
      console.warn(`[CreateTask] 未找到URL对应的数据信息: ${url}`)
      continue
    }

    // 根据任务类型计算文件大小
    const urlWithType = allUrlsWithType.value.find(item => item.url === url)
    if (!urlWithType) {
      console.warn(`[CreateTask] 未找到URL对应的任务类型信息: ${url}`)
      continue
    }

    const { taskType } = urlWithType

    switch (taskType) {
      case BaseType.TaskType.Magnet:
        // 磁力链任务：计算选中文件的大小
        if (dataInfo.fileLists && Array.isArray(dataInfo.fileLists)) {
          for (const fileIndex of selectionInfo.fileIndexes) {
            const file = dataInfo.fileLists[fileIndex]
            if (file && typeof file.fileSize === 'number') {
              totalSelectedFileSize += file.fileSize
            }
          }
        }
        break

      case BaseType.TaskType.P2sp:
        // P2SP任务：使用单个文件的大小
        if (typeof dataInfo.fileSize === 'number') {
          totalSelectedFileSize += dataInfo.fileSize
        }
        break

      case BaseType.TaskType.Emule:
        // Emule任务：使用单个文件的大小
        if (typeof dataInfo.fileSize === 'number') {
          totalSelectedFileSize += dataInfo.fileSize
        }
        break

      default:
        console.warn(`[CreateTask] 未知的任务类型: ${taskType}, URL: ${url}`)
        break
    }
  }

  console.log(`[CreateTask] 选中文件总大小: ${totalSelectedFileSize} 字节`)
  return totalSelectedFileSize
}

/**
 * 获取逻辑驱动器字符串和空间信息
 * 先调用 ThunderHelper 的 getLogicalDriveStrings 方法获取逻辑驱动器字符串
 * 再调用 ThunderHelper.getFreePartitionSpace 获取每个驱动器的空间大小
 * @returns Array<{drive: string, freeSpace: number, totalSpace: number}> 返回驱动器及其剩余空间信息数组
 */
function getLogicalDriveList(): Array<{ drive: string; freeSpace: number; totalSpace: number }> {
  try {
    console.log(`[CreateTask] 获取逻辑驱动器字符串`)

    // 调用 ThunderHelper 的 getLogicalDriveStrings 方法获取驱动器列表
    const logicalDriveStrings = ThunderHelper.getLogicalDriveStrings()

    console.log(`[CreateTask] 逻辑驱动器字符串: ${logicalDriveStrings}`)

    // 对每个驱动器获取剩余空间信息
    const driveSpaceInfo: Array<{ drive: string; freeSpace: number; totalSpace: number }> = []

    for (const drive of logicalDriveStrings) {
      try {
        // 调用 ThunderHelper.getFreePartitionSpace 获取该驱动器的剩余空间
        const freeSpace = ThunderHelper.getFreePartitionSpace(drive)
        const space = ThunderHelper.getPartitionSpace(drive)

        driveSpaceInfo.push({
          drive: drive,
          freeSpace: space.free,
          totalSpace: space.total,
        })

        console.log(`[CreateTask] 驱动器 ${drive} 剩余空间: ${freeSpace} 字节`)
      } catch (error) {
        console.warn(`[CreateTask] 获取驱动器 ${drive} 空间信息失败:`, error)
        // 如果获取失败，仍然添加到结果中，但空间设为 -1
        driveSpaceInfo.push({
          drive: drive,
          freeSpace: -1,
          totalSpace: -1,
        })
      }
    }

    console.log(`[CreateTask] 驱动器空间信息:`, driveSpaceInfo)
    return driveSpaceInfo
  } catch (error) {
    console.error('[CreateTask] 获取逻辑驱动器字符串失败:', error)
    return []
  }
}



// 新增：处理右键菜单项点击
const handleContextMenuAction = (action: string) => {
  console.log('执行右键菜单操作:', action)
  // 新组件已经处理了具体的操作，这里只需要记录日志
}

// 磁盘空间不足状态给子组件
provide('isDiskSpaceInsufficient', isDiskSpaceInsufficient)
// 云添加剩余次数给子组件
provide('freeCloudAddCount', freeCloudAddCount)
// 窗口失焦状态给子组件
provide('isWindowBlurred', isWindowBlurred)
</script>

<template>
  <div
    v-if="userStore.isLogged"
    :class="{ 'window-blurred': isWindowBlurred }"
  >
    <div
      class="pre-new-task"
      v-if="isAutoCreateTask"
    >
      <!-- 自动创建任务 start -->
      <AutoCreateTaskComponent
        v-if="allUrlsWithType.length > 0"
        :task-data="allUrlsWithType"
        :data-map="dataMap"
        :options-ext-data="optionsExtData"
        :options="options as any"
        @close="handleAutoCreateTaskClose"
      ></AutoCreateTaskComponent>
    </div>

    <div v-else>
      <Dialog
        :open="dialogVisible"
        :showCancel="false"
        preventDefaultClose
        @update:open="
          (val: boolean) => {
            dialogVisible = val
            if (!val) closeWindow('dialog_update')
          }
        "
        title="新建任务"
        confirmText=""
        show-title-icon
        cancelText="稍后"
        @confirm="handleConfirm"
        @cancel="handleCancel"
        @close="handleDialogClose"
        :loading="isLoading"
        classPrefix="create-task"
        :showSelectionCount="true"
        :isCreateTask="true"
        :class="{ 'dialog-blurred': isWindowBlurred }"
      >
        <div class="create-task-content none-draggable">
          <!-- Magnet link input -->

          <div class="url-input-container">
            <textarea
              class="magnet-input allow-context-menu"
              v-model="inputValue"
              type="text"
              placeholder="添加多个下裁链接时，请确保每行只有一个链接，输入回车键分行"
            />
          </div>

          <!-- 解析后的文件列表 -->

          <div
            class="file-list-container"
            v-if="showTaskList"
          >
            <div class="file-list-wrapper">
              <!-- 任务列表 start -->
              <TaskList
                :containerHeight="314"
                :task-data="allUrlsWithType"
                :data-map="dataMap"
                :options-ext-data="optionsExtData"
                :table-height="200"
                :show-selection-count="true"
                @checkedFileIndexes="handleCheckedFileIndexes"
              />
              <!-- 任务列表 end -->
            </div>
          </div>
        </div>

        <template #right-action>
          <div class="create-task-actions">
            <!-- 播放按钮 - 只在有可播放任务时显示 -->
            <PlayButton
              class="play-button"
              :checkedFileIndexes="checkedFileIndexes"
              :dataMap="dataMap"
              :taskData="allUrlsWithType"
              :optionsExtData="optionsExtData"
              :buttonText="'播放'"
              scene="create-task-main"
            />

            <DownloadButton
              ref="downloadButtonRef"
              scene="create-task-main"
              @submit="handleDownloadButtonSubmit"
              :disabled="!hasSelectedFiles"
              :selected-path-type="selectedPathType"
            ></DownloadButton>
          </div>
        </template>

        <!-- 添加左侧稍后按钮 -->

        <template #left-action>
          <TaskLaterButton
            :task-data="allUrlsWithType"
            :data-map="dataMap"
            :options-ext-data="optionsExtData"
            :url-extra-data-map="urlExtraDataMap"
            :checked-file-indexes="checkedFileIndexes"
            :has-valid-tasks="hasValidTasks"
            :disabled="!hasValidTasks"
            scene="create-task-main"
            @success="handleLaterSuccess"
            @error="handleLaterError"
            @cancel="() => closeWindow('task_later_cancel')"
          >
            稍后
          </TaskLaterButton>
        </template>
      </Dialog>

      <!-- 添加textarea右键菜单组件 -->
      <TextareaContextMenu
        target-selector=".magnet-input"
        @action="handleContextMenuAction"
      />
    </div>
  </div>
</template>

<style scoped lang="scss">
@import '@root/modal-renderer/src/views/create-task/scss/create-task.scss';
</style>
